# 🔖 精选导航 - 书签导航系统

一个现代化的书签管理和导航系统，支持三级分类管理、智能搜索、图标自动获取等功能。专为个人和团队打造的专属书签导航平台。

![项目预览](https://img.shields.io/badge/Node.js-16.0+-green) ![数据库](https://img.shields.io/badge/MySQL-8.0+-blue) ![前端](https://img.shields.io/badge/Tailwind-CSS-cyan) ![许可证](https://img.shields.io/badge/License-MIT-yellow)

## 📸 项目预览

### 🏠 首页界面
- **现代化设计**：毛玻璃效果 + 深色主题支持
- **智能布局**：5列响应式网格，完美适配各种屏幕
- **实时搜索**：输入即搜，支持快捷键操作

### ⚙️ 管理后台
- **直观管理**：可视化的分类和书签管理界面
- **数据统计**：实时显示分类、书签数量和访问统计
- **批量操作**：支持拖拽排序和批量编辑

### 📱 移动端适配
- **响应式设计**：完美适配手机和平板设备
- **触摸优化**：针对移动端优化的交互体验
- **侧边栏导航**：移动端友好的导航方式

## ✨ 功能特性

### 🎯 核心功能
- **三级分类结构** - 分类 > 子分类 > 书签的层级管理
- **智能图标系统** - 自动获取网站Favicon，支持MDI图标和彩色字母备用
- **实时搜索** - 支持书签标题、描述、URL的模糊搜索
- **点击统计** - 记录书签访问次数，支持热门排序
- **响应式设计** - 完美适配桌面端和移动端

### 🔧 管理功能
- **简化认证** - 仅需密码登录，无复杂用户系统
- **可视化管理** - 直观的后台管理界面
- **数据统计** - 分类、书签数量和点击统计
- **批量操作** - 支持书签的批量管理

### 🎨 用户体验
- **现代UI** - 基于Tailwind CSS的现代化界面
- **快速加载** - 图标缓存和延迟加载优化
- **键盘快捷键** - Ctrl+K快速搜索，ESC清除搜索
- **移动友好** - 侧边栏和触摸优化

## 🚀 快速开始

### 环境要求
- **Node.js** 16.0+ (推荐 18.0+)
- **MySQL** 8.0+ (或 MariaDB 10.3+)
- **现代浏览器** (Chrome 90+, Firefox 88+, Safari 14+)
- **操作系统** Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd bookmarks-navigator
```

2. **安装依赖**
```bash
# 双击运行（推荐）
install.bat

# 或手动安装
npm install
```

3. **配置数据库**
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE bookmarks_db;

# 导入数据结构
mysql -u root -p bookmarks_db < sql/00_complete_database_init.sql
```

4. **配置环境变量**
```bash
# 编辑 .env 文件
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=bookmarks_db
PORT=3006
ADMIN_PASSWORD=admin123456
```

5. **启动项目**
```bash
# 双击运行（推荐）
start.bat

# 或手动启动
npm run dev
```

6. **访问系统**
- 🏠 **首页**：http://127.0.0.1:3006
- ⚙️ **管理后台**：http://127.0.0.1:3006/admin
- 🔑 **登录页面**：http://127.0.0.1:3006/login
- 🔐 **默认密码**：`admin123456`

> 💡 **提示**：首次启动会自动创建示例数据，包含常用网站分类和书签

## 📁 项目结构

```
bookmarks-navigator/
├── 📁 config/              # 配置文件
│   └── database.js         # 数据库配置
├── 📁 middleware/          # 中间件
│   └── errorHandler.js     # 错误处理
├── 📁 public/              # 前端文件
│   ├── index.html          # 首页
│   ├── login.html          # 登录页
│   ├── admin.html          # 管理后台
│   ├── login.js            # 登录逻辑
│   └── admin.js            # 管理后台逻辑
├── 📁 routes/              # 路由文件
│   ├── api.js              # 前台API
│   ├── auth.js             # 认证API
│   └── admin.js            # 管理API
├── 📁 sql/                 # 数据库文件
│   ├── 00_complete_database_init.sql  # 完整初始化
│   └── 01_bookmarks_core.sql          # 核心表结构
├── 📁 utils/               # 工具函数
│   └── mysqlDb.js          # 数据库工具
├── 📄 server.js            # 主服务器
├── 📄 package.json         # 项目配置
├── 📄 install.bat          # 安装依赖脚本
└── 📄 start.bat            # 启动项目脚本
```

## 🎯 使用指南

### 管理员操作

1. **登录管理后台**
   - 访问 `/admin` 或点击首页右上角管理按钮
   - 输入密码：`admin123456`

2. **管理分类**
   - 添加/编辑/删除主分类
   - 设置分类图标和颜色
   - 调整分类排序

3. **管理子分类**
   - 在主分类下创建子分类
   - 设置子分类属性
   - 管理子分类排序

4. **管理书签**
   - 添加新书签到指定子分类
   - 编辑书签信息（标题、URL、描述、图标）
   - 查看点击统计
   - 批量管理书签

### 用户操作

1. **浏览书签**
   - 点击左侧分类导航
   - 浏览不同子分类的书签
   - 点击书签访问网站

2. **搜索功能**
   - 使用顶部搜索框
   - 支持标题、描述、URL搜索
   - 快捷键：`Ctrl + K` 聚焦搜索框

3. **移动端使用**
   - 点击左上角菜单按钮展开导航
   - 支持触摸滑动和点击操作

## 🔧 技术架构

### 后端技术
- **Node.js** - 服务器运行环境
- **Express.js** - Web框架
- **MySQL2** - 数据库驱动
- **bcrypt** - 密码加密
- **dotenv** - 环境变量管理

### 前端技术
- **原生JavaScript** - 核心逻辑
- **Tailwind CSS** - 样式框架
- **Material Design Icons** - 图标库
- **Fetch API** - 数据请求

### 数据库设计
| 表名 | 描述 | 主要字段 |
|------|------|----------|
| `categories` | 主分类表 | `category_id`, `category_title`, `category_icon`, `category_sort` |
| `subcategories` | 子分类表 | `subcategory_id`, `category_id`, `subcategory_title`, `subcategory_sort` |
| `bookmarks` | 书签表 | `bookmark_id`, `subcategory_id`, `bookmark_title`, `bookmark_url`, `click_count` |
| `users` | 管理员表 | `user_id`, `username`, `password_hash`, `user_type` |
| `user_sessions` | 会话表 | `session_id`, `user_id`, `expires_at`, `ip_address` |
| `operation_logs` | 操作日志表 | `log_id`, `user_id`, `operation_type`, `operation_detail` |

### 特色功能
- **图标系统** - Favicon自动获取 + MDI图标 + 字母图标
- **缓存机制** - 30天Favicon本地缓存
- **搜索引擎** - 实时模糊搜索
- **响应式布局** - 5列网格自适应

## 🛠️ 开发指南

### 本地开发
```bash
# 开发模式（支持热重载）
npm run dev

# 生产模式
npm start
```

### 数据库操作
```bash
# 重置数据库
mysql -u root -p bookmarks_db < sql/00_complete_database_init.sql

# 备份数据库
mysqldump -u root -p bookmarks_db > backup.sql
```

### 环境配置
```env
# 开发环境
NODE_ENV=development
PORT=3006

# 数据库配置
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=bookmarks_db

# 管理员配置
ADMIN_PASSWORD=admin123456
```

## 📊 API文档

### 🌐 前台API
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| `GET` | `/api/categories` | 获取所有分类和书签 | 无 |
| `POST` | `/api/bookmarks/:id/click` | 记录书签点击 | `id`: 书签ID |

### 🔐 认证API
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| `POST` | `/api/auth/login` | 管理员登录 | `username`, `password` |
| `POST` | `/api/auth/logout` | 管理员登出 | 无 |
| `GET` | `/api/auth/verify` | 验证会话 | Header: `Authorization` |

### ⚙️ 管理API
| 方法 | 路径 | 描述 | 参数 |
|------|------|------|------|
| `GET` | `/admin/api/stats` | 获取统计数据 | 无 |
| `GET` | `/admin/api/categories` | 获取分类列表 | 无 |
| `POST` | `/admin/api/categories` | 创建分类 | `category_title`, `category_icon` |
| `PUT` | `/admin/api/categories/:id` | 更新分类 | `id`, 分类数据 |
| `DELETE` | `/admin/api/categories/:id` | 删除分类 | `id` |
| `GET` | `/admin/api/bookmarks` | 获取书签列表 | `subcategory_id` (可选) |
| `POST` | `/admin/api/bookmarks` | 创建书签 | `bookmark_title`, `bookmark_url` |
| `PUT` | `/admin/api/bookmarks/:id` | 更新书签 | `id`, 书签数据 |
| `DELETE` | `/admin/api/bookmarks/:id` | 删除书签 | `id` |

## 🎨 自定义配置

### 修改管理员密码
编辑 `.env` 文件中的 `ADMIN_PASSWORD` 字段

### 修改端口
编辑 `.env` 文件中的 `PORT` 字段

### 添加自定义图标
在管理后台的书签编辑中，使用 `mdi-` 前缀的图标名称

### 自定义样式
编辑 `public/` 目录下的HTML文件中的CSS样式

## 🔍 故障排除

### 常见问题

#### 🔌 数据库连接失败
```bash
# 检查MySQL服务状态
systemctl status mysql  # Linux
net start mysql         # Windows

# 测试数据库连接
mysql -u root -p -h 127.0.0.1
```
**解决方案**：
- 确认MySQL服务已启动
- 验证 `.env` 中的数据库配置
- 检查数据库是否已创建：`CREATE DATABASE bookmarks_db;`

#### 🚪 端口被占用
```bash
# 查看端口占用情况
netstat -ano | findstr :3006  # Windows
lsof -i :3006                 # macOS/Linux
```
**解决方案**：
- 修改 `.env` 中的 `PORT` 配置
- 或终止占用端口的进程

#### 📦 依赖安装失败
```bash
# 使用国内镜像
npm config set registry https://registry.npmmirror.com

# 清理缓存
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

#### 🖼️ 图标不显示
**可能原因**：
- 网络连接问题（Favicon需要访问外部服务）
- 浏览器缓存问题
- CDN资源加载失败

**解决方案**：
- 检查网络连接和防火墙设置
- 清除浏览器缓存：`Ctrl+Shift+Delete`
- 使用开发者工具查看控制台错误

### 🚀 性能优化

#### 数据库优化
```sql
-- 添加常用索引
CREATE INDEX idx_bookmarks_subcategory_sort ON bookmarks(subcategory_id, bookmark_sort);
CREATE INDEX idx_subcategories_category_sort ON subcategories(category_id, subcategory_sort);
CREATE INDEX idx_bookmarks_click_count ON bookmarks(click_count DESC);

-- 定期清理过期会话
DELETE FROM user_sessions WHERE expires_at < NOW();
```

#### 前端优化
```nginx
# Nginx缓存配置
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary Accept-Encoding;
    gzip_static on;
}
```

#### 应用优化
- **图标缓存**：30天本地缓存，减少网络请求
- **延迟加载**：书签图标按需加载，提升首屏速度
- **数据压缩**：启用gzip压缩，减少传输大小
- **连接池**：MySQL连接池优化，提升并发性能

### 🔒 安全特性

#### 认证安全
- **会话管理**：安全的会话令牌，自动过期机制
- **密码保护**：环境变量存储，避免硬编码
- **IP记录**：登录IP地址记录，便于安全审计
- **操作日志**：完整的管理操作日志记录

#### 数据安全
- **SQL注入防护**：使用参数化查询，防止SQL注入
- **XSS防护**：输入数据过滤和转义
- **CSRF防护**：请求令牌验证
- **数据备份**：支持数据库备份和恢复

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 🌟 功能亮点

### 智能图标系统
- **三层优先级**：Favicon > MDI图标 > 字母图标
- **自动缓存**：30天本地缓存，提升加载速度
- **高清显示**：64px高清Favicon，视觉效果佳
- **容错机制**：多层降级，确保总有图标显示

### 搜索体验
- **实时搜索**：输入即搜，无需等待
- **全文检索**：标题、描述、URL全覆盖
- **快捷操作**：Ctrl+K聚焦，ESC清除
- **结果高亮**：匹配内容智能显示

### 管理便捷
- **一键部署**：双击bat文件即可启动
- **简化认证**：只需密码，无复杂注册
- **可视化操作**：拖拽排序，直观管理
- **数据统计**：实时统计，一目了然

## 🎯 使用场景

### 个人用户
- **收藏夹管理** - 替代浏览器书签，更好的分类和搜索
- **常用网站导航** - 快速访问工作和生活常用网站
- **学习资源整理** - 按主题分类学习网站和工具

### 团队使用
- **团队导航页** - 统一的团队常用工具和资源入口
- **项目资源管理** - 按项目分类相关网站和文档
- **知识库导航** - 企业内部系统和外部资源的统一入口

### 企业应用
- **员工门户** - 企业内部系统和常用工具的导航页
- **客户服务** - 为客户提供相关资源和服务的快速入口
- **合作伙伴** - 合作伙伴资源和工具的集中展示

## 🔧 高级配置

### 数据库优化
```sql
-- 添加索引提升查询性能
CREATE INDEX idx_bookmarks_subcategory ON bookmarks(subcategory_id);
CREATE INDEX idx_subcategories_category ON subcategories(category_id);
CREATE INDEX idx_bookmarks_sort ON bookmarks(bookmark_sort);
```

### Nginx配置（生产环境）
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:3006;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /static/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### PM2部署配置
```json
{
  "name": "bookmarks-navigator",
  "script": "server.js",
  "instances": 1,
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "PORT": 3006
  },
  "log_date_format": "YYYY-MM-DD HH:mm:ss",
  "error_file": "./logs/err.log",
  "out_file": "./logs/out.log",
  "log_file": "./logs/combined.log"
}
```

## 🚀 生产环境部署

### Docker 部署（推荐）
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3006

CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  bookmarks-app:
    build: .
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_USER=bookmarks
      - DB_PASSWORD=your_password
      - DB_NAME=bookmarks_db
      - ADMIN_PASSWORD=your_admin_password
    depends_on:
      - mysql
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=bookmarks_db
      - MYSQL_USER=bookmarks
      - MYSQL_PASSWORD=your_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
    restart: unless-stopped

volumes:
  mysql_data:
```

### 传统部署
```bash
# 1. 服务器环境准备
sudo apt update
sudo apt install nodejs npm mysql-server nginx

# 2. 克隆项目
git clone <repository-url>
cd bookmarks-navigator

# 3. 安装依赖
npm ci --only=production

# 4. 配置数据库
sudo mysql -u root -p
CREATE DATABASE bookmarks_db;
CREATE USER 'bookmarks'@'127.0.0.1' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON bookmarks_db.* TO 'bookmarks'@'127.0.0.1';
FLUSH PRIVILEGES;

# 5. 导入数据结构
mysql -u bookmarks -p bookmarks_db < sql/00_complete_database_init.sql

# 6. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 7. 启动服务
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### SSL证书配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://127.0.0.1:3006;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 📈 更新日志

### v1.0.0 (2025-06-15)
- ✨ **初始版本发布**
- 🎯 **三级分类书签管理系统** - 分类 > 子分类 > 书签的完整层级结构
- 🎨 **智能图标获取和缓存** - 自动获取Favicon，支持MDI图标和字母图标备用
- 🔍 **实时搜索功能** - 支持标题、描述、URL的模糊搜索，快捷键支持
- 📱 **响应式设计** - 完美适配桌面端和移动端，5列网格自适应布局
- 🔐 **简化认证系统** - 仅需密码登录，无复杂用户注册流程
- 📊 **数据统计功能** - 实时统计分类、书签数量和点击次数
- 🎨 **现代化UI** - 基于Tailwind CSS的毛玻璃效果和深色主题支持
- 🚀 **一键部署** - 提供install.bat和start.bat脚本，简化部署流程

## 🚀 路线图

### 计划功能
- [ ] **导入导出** - 支持浏览器书签导入/导出
- [ ] **主题切换** - 深色模式和多主题支持
- [ ] **标签系统** - 为书签添加标签分类
- [ ] **访问统计** - 更详细的访问分析
- [ ] **API扩展** - 提供更完整的REST API
- [ ] **多用户支持** - 支持多用户独立管理
- [ ] **备份恢复** - 自动备份和一键恢复
- [ ] **插件系统** - 支持第三方插件扩展

### 技术改进
- [ ] **TypeScript** - 代码类型安全
- [ ] **Vue.js/React** - 现代前端框架
- [ ] **Docker** - 容器化部署
- [ ] **Redis缓存** - 提升性能
- [ ] **全文搜索** - Elasticsearch集成
- [ ] **CDN支持** - 静态资源加速

## 📞 支持

### 获取帮助
- 📖 **文档** - 查看完整的使用文档
- 🐛 **问题反馈** - 提交GitHub Issue
- 💬 **讨论** - 参与GitHub Discussions
- 📧 **联系** - 发送邮件获取支持

### 贡献指南
1. Fork项目到你的GitHub
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 开发环境
```bash
# 克隆开发版本
git clone -b develop <repository-url>

# 安装开发依赖
npm install --include=dev

# 启动开发服务器
npm run dev

# 运行测试
npm test
```

---

<div align="center">

## 🎉 感谢使用精选导航系统！

如果这个项目对你有帮助，请给个 ⭐ **Star** 支持一下！

### 快速链接
[![首页](https://img.shields.io/badge/🏠-首页-blue?style=for-the-badge)](http://127.0.0.1:3006)
[![管理后台](https://img.shields.io/badge/⚙️-管理后台-green?style=for-the-badge)](http://127.0.0.1:3006/admin)
[![文档](https://img.shields.io/badge/📚-文档-orange?style=for-the-badge)](#)
[![反馈](https://img.shields.io/badge/🐛-反馈-red?style=for-the-badge)](#)

### 技术栈
![Node.js](https://img.shields.io/badge/Node.js-339933?style=flat&logo=node.js&logoColor=white)
![Express](https://img.shields.io/badge/Express-000000?style=flat&logo=express&logoColor=white)
![MySQL](https://img.shields.io/badge/MySQL-4479A1?style=flat&logo=mysql&logoColor=white)
![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=flat&logo=tailwind-css&logoColor=white)
![JavaScript](https://img.shields.io/badge/JavaScript-F7DF1E?style=flat&logo=javascript&logoColor=black)

**Made with ❤️ for better bookmark management**

</div>
