const mysql = require('mysql2/promise');

const pool = mysql.createPool({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'root',
    database: process.env.DB_NAME || 'bookmarks_db',
    charset: 'utf8mb4',
    timezone: '+08:00',
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

module.exports = {
    // 执行SQL查询并返回结果
    execute: async (sql, params = []) => {
        try {
            // 对于事务命令，使用query而不是execute
            if (sql.trim().toUpperCase().startsWith('START TRANSACTION') ||
                sql.trim().toUpperCase().startsWith('COMMIT') ||
                sql.trim().toUpperCase().startsWith('ROLLBACK')) {
                return await pool.query(sql, params);
            }
            return await pool.execute(sql, params);
        } catch (error) {
            console.error('SQL执行错误:', error);
            throw error;
        }
    },

    // 执行SQL查询并只返回数据行
    query: async (sql, params = []) => {
        try {
            const [rows] = await pool.execute(sql, params);
            return rows;
        } catch (error) {
            console.error('SQL查询错误:', error);
            throw error;
        }
    },

    // 获取原始连接池（用于特殊情况）
    getPool: () => pool,

    // 获取连接（用于事务）
    getConnection: async () => {
        try {
            return await pool.getConnection();
        } catch (error) {
            console.error('获取连接失败:', error);
            throw error;
        }
    }
};