@echo off
setlocal enabledelayedexpansion

title Install Dependencies

cls
echo.
echo ========================================
echo    Bookmarks Navigator - Install
echo ========================================
echo.

echo [1/3] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo OK: Node.js !NODE_VERSION!
)

echo.
echo [2/3] Cleaning old dependencies...
if exist "node_modules" (
    echo Removing old node_modules...
    rmdir /s /q "node_modules" 2>nul
)

if exist "package-lock.json" (
    echo Removing old package-lock.json...
    del "package-lock.json" 2>nul
)

echo.
echo [3/3] Installing dependencies...
call npm install
if errorlevel 1 (
    echo.
    echo ERROR: Failed to install dependencies
    echo Try: npm config set registry https://registry.npmmirror.com
    pause
    exit /b 1
) else (
    echo.
    echo SUCCESS: Dependencies installed
    echo Now you can run start.bat to launch the project
)

echo.
pause
exit /b 0
