const errorHandler = (err, req, res, next) => {
    console.error('❌ 服务器错误:', err);

    // 数据库连接错误
    if (err.code === 'ECONNREFUSED') {
        return res.status(503).json({
            success: false,
            message: '数据库连接失败，请检查数据库服务是否启动'
        });
    }

    // MySQL错误
    if (err.code && err.code.startsWith('ER_')) {
        return res.status(500).json({
            success: false,
            message: '数据库操作失败'
        });
    }

    // SQL错误
    if (err.code === 'ER_NO_SUCH_TABLE') {
        return res.status(500).json({
            success: false,
            message: '数据表不存在'
        });
    }

    // JSON解析错误
    if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
        return res.status(400).json({
            success: false,
            message: '请求数据格式错误'
        });
    }

    // 默认错误
    res.status(err.status || 500).json({
        success: false,
        message: err.message || '服务器内部错误',
        stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
};

module.exports = errorHandler;