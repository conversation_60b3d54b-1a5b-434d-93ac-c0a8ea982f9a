{"name": "has-unicode", "version": "2.0.1", "description": "Try to guess if your terminal supports unicode", "main": "index.js", "scripts": {"test": "tap test/*.js"}, "repository": {"type": "git", "url": "https://github.com/iarna/has-unicode"}, "keywords": ["unicode", "terminal"], "files": ["index.js"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/iarna/has-unicode/issues"}, "homepage": "https://github.com/iarna/has-unicode", "devDependencies": {"require-inject": "^1.3.0", "tap": "^2.3.1"}}