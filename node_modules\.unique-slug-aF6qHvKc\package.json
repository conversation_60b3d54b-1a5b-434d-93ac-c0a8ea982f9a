{"name": "unique-slug", "version": "2.0.2", "description": "Generate a unique character string suitible for use in files and URLs.", "main": "index.js", "scripts": {"test": "standard && tap --coverage test"}, "keywords": [], "author": "<PERSON> <<EMAIL>> (http://re-becca.org)", "license": "ISC", "devDependencies": {"standard": "^12.0.1", "tap": "^12.7.0"}, "repository": {"type": "git", "url": "git://github.com/iarna/unique-slug.git"}, "dependencies": {"imurmurhash": "^0.1.4"}}