"use strict";window.createLRU=function(e){var r=e.max,n=e.onEviction;if(!(Number.isInteger(r)&&r>0))throw new TypeError("`max` must be a positive integer");var i=0,t=0,a=0,o=[],l=new Map,f=new Array(r).fill(void 0),u=new Array(r).fill(void 0),v=new Array(r).fill(0),s=new Array(r).fill(0),d=function(e,r){if(e!==a){var n=v[e],i=s[e];e===t?t=n:("get"===r||0!==i)&&(v[i]=n),0!==n&&(s[n]=i),v[a]=e,s[e]=a,v[e]=0,a=e}},p=function(){var e=t,r=f[e];return null==n||n(r,u[e]),l.delete(r),f[e]=void 0,u[e]=void 0,0!==(t=v[e])&&(s[t]=0),0===--i&&(t=a=0),o.push(e),e};return{set:function(e,v){if(void 0!==e){var s=l.get(e);void 0===s?(s=i===r?p():o.length>0?o.pop():i,l.set(e,s),f[s]=e,i++):null==n||n(e,u[s]),u[s]=v,1===i?t=a=s:d(s,"set")}},get:function(e){var r=l.get(e);if(void 0!==r)return r!==a&&d(r,"get"),u[r]},peek:function(e){var r=l.get(e);return void 0!==r?u[r]:void 0},has:function(e){return l.has(e)},keys:function*(){for(var e=a,r=0;r<i;r++)yield f[e],e=s[e]},values:function*(){for(var e=a,r=0;r<i;r++)yield u[e],e=s[e]},entries:function*(){for(var e=a,r=0;r<i;r++)yield[f[e],u[e]],e=s[e]},forEach:function(e){for(var r=a,n=0;n<i;n++){var t=f[r];e(u[r],t),r=s[r]}},delete:function(e){var r=l.get(e);if(void 0===r)return!1;null==n||n(e,u[r]),l.delete(e),o.push(r),f[r]=void 0,u[r]=void 0;var d=s[r],p=v[r];return 0!==d&&(v[d]=p),0!==p&&(s[p]=d),r===t&&(t=p),r===a&&(a=d),i--,!0},evict:function(e){for(var r=Math.min(e,i);r>0;)p(),r--},clear:function(){if("function"==typeof n)for(var e=l.values(),r=e.next();!r.done;r=e.next())n(f[r.value],u[r.value]);l.clear(),f.fill(void 0),u.fill(void 0),o=[],i=0,t=a=0},resize:function(e){if(!(Number.isInteger(e)&&e>0))throw new TypeError("`max` must be a positive integer");if(e!==r){if(e<r){for(var d=a,p=Math.min(i,e),c=i-p,y=new Array(e),g=new Array(e),h=new Array(e),w=new Array(e),A=1;A<=c;A++)null==n||n(f[A],u[A]);for(var m=p-1;m>=0;m--)y[m]=f[d],g[m]=u[d],h[m]=m+1,w[m]=m-1,l.set(y[m],m),d=s[d];t=0,a=p-1,i=p,f.length=e,u.length=e,v.length=e,s.length=e;for(var x=0;x<p;x++)f[x]=y[x],u[x]=g[x],v[x]=h[x],s[x]=w[x];o=[];for(var b=p;b<e;b++)o.push(b)}else{var E=e-r;f.push.apply(f,new Array(E).fill(void 0)),u.push.apply(u,new Array(E).fill(void 0)),v.push.apply(v,new Array(E).fill(0)),s.push.apply(s,new Array(E).fill(0))}r=e}},get max(){return r},get size(){return i},get available(){return r-i}}};