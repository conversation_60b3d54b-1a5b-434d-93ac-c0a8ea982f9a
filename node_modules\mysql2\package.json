{"name": "mysql2", "version": "3.11.4", "description": "fast mysql driver. Implements core protocol, prepared statements, ssl and compression in native JS", "main": "index.js", "typings": "typings/mysql/index", "scripts": {"lint": "npm run lint:docs && npm run lint:code", "lint:code": "eslint index.js promise.js index.d.ts promise.d.ts \"typings/**/*.ts\" \"lib/**/*.js\" \"test/**/*.{js,cjs,mjs,ts}\" \"benchmarks/**/*.js\"", "lint:docs": "eslint Contributing.md README.md", "lint:typings": "npx prettier --check ./typings", "lint:tests": "npx prettier --check ./test", "test": "poku --debug --include=\"test/esm,test/unit,test/integration\"", "test:bun": "poku --debug --platform=\"bun\" --include=\"test/esm,test/unit,test/integration\"", "test:deno": "deno run --allow-read --allow-env --allow-run npm:poku --debug --platform=\"deno\" --deno-allow=\"read,env,net,sys\" --deno-cjs=\".js,.cjs\" --include=\"test/esm,test/unit,test/integration\"", "test:tsc-build": "cd \"test/tsc-build\" && npx tsc -p \"tsconfig.json\"", "coverage-test": "c8 npm run test", "benchmark": "node ./benchmarks/benchmark.js", "prettier": "prettier --single-quote --trailing-comma none --write \"{lib,test}/**/*.js\"", "prettier:docs": "prettier --single-quote --trailing-comma none --write README.md", "precommit": "lint-staged", "eslint-check": "eslint --print-config .eslintrc | eslint-config-prettier-check", "wait-port": "wait-on"}, "lint-staged": {"*.js": ["prettier --single-quote --trailing-comma none --write", "git add"]}, "repository": {"type": "git", "url": "https://github.com/sidorares/node-mysql2"}, "homepage": "https://sidorares.github.io/node-mysql2/docs", "keywords": ["mysql", "client", "server"], "files": ["lib", "typings/mysql", "index.js", "index.d.ts", "promise.js", "promise.d.ts"], "exports": {".": "./index.js", "./package.json": "./package.json", "./promise": "./promise.js", "./promise.js": "./promise.js"}, "engines": {"node": ">= 8.0"}, "author": "<PERSON><PERSON> <and<PERSON>.<EMAIL>>", "license": "MIT", "dependencies": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.6.3", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "devDependencies": {"@types/node": "^22.0.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "@typescript-eslint/parser": "^5.42.1", "assert-diff": "^3.0.2", "benchmark": "^2.1.4", "c8": "^10.1.1", "error-stack-parser": "^2.0.3", "eslint": "^8.27.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-async-await": "0.0.0", "eslint-plugin-markdown": "^5.0.0", "lint-staged": "^15.0.1", "poku": "^2.0.0", "portfinder": "^1.0.28", "prettier": "^3.0.0", "progress": "^2.0.3", "typescript": "^5.0.2"}}