<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>书签管理后台</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="manifest" href="/site.webmanifest">
    <meta name="theme-color" content="#667eea">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>


        .btn-green {
            background-color: #10b981;
            color: white;
        }

        .btn-green:hover {
            background-color: #059669;
        }
    </style>
    <style>
        .sidebar-item.active {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(99, 102, 241, 0.05) 100%);
            color: #6366f1;
            border-left: 4px solid #6366f1;
            font-weight: 600;
            transform: translateX(4px);
        }

        .sidebar-item:hover {
            transform: translateX(2px);
        }

        .table-container {
            max-height: 65vh;
            overflow-y: auto;
        }

        .modal {
            backdrop-filter: blur(10px);
        }

        /* 自定义滚动条 */
        .table-container::-webkit-scrollbar {
            width: 6px;
        }

        .table-container::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 仪表板特殊布局 */
        .dashboard-layout {
            min-height: calc(100vh - 12rem);
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            padding: 2rem 0;
        }

        /* 仪表板标题区域 */
        .dashboard-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .dashboard-subtitle {
            font-size: 1.125rem;
            color: #6b7280;
            font-weight: 400;
        }

        /* 统计卡片样式优化 */
        .stats-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            border: 1px solid #f3f4f6;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
        }

        .stats-icon {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.75rem;
        }

        .stats-number {
            font-size: 2.25rem;
            font-weight: 700;
            color: #111827;
            line-height: 1;
        }

        .stats-label {
            font-size: 0.875rem;
            font-weight: 500;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        /* 其他页面需要滚动 */
        .content-section:not(.dashboard-layout) {
            min-height: calc(100vh - 8rem);
        }

        /* 仪表板网格布局 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .dashboard-title {
                font-size: 2rem;
            }
        }

        /* 树形列表样式 */
        .category-row {
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .category-row:hover {
            background-color: #f9fafb;
        }

        .category-row.expanded {
            background-color: #f0f9ff;
        }

        .subcategory-row {
            border-bottom: 1px solid #f3f4f6;
            background-color: #fafbfc;
            display: none;
        }

        .subcategory-row.show {
            display: table-row;
        }

        .expand-icon {
            transition: transform 0.2s;
            display: inline-block;
        }

        .expand-icon.expanded {
            transform: rotate(90deg);
        }

        .category-title {
            font-weight: 600;
            color: #1f2937;
        }

        .subcategory-title {
            font-weight: 500;
            color: #4b5563;
            padding-left: 2rem;
        }

        /* 自定义确认弹窗样式 */
        .confirm-modal {
            backdrop-filter: blur(8px);
            animation: fadeIn 0.2s ease-out;
        }

        .confirm-modal-content {
            animation: slideIn 0.3s ease-out;
            transform-origin: center;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .warning-icon {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* 统一按钮样式系统 */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
        }

        .btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
        }

        /* 按钮尺寸 */
        .btn-sm {
            padding: 0.5rem 1rem;
            font-size: 0.75rem;
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1rem;
        }

        /* 主要操作按钮 - 蓝色系 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
        }

        /* 成功/添加按钮 - 绿色系 */
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        /* 警告/编辑按钮 - 橙色系 */
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
        }

        /* 危险/删除按钮 - 红色系 */
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }

        /* 特殊功能按钮 - 紫色系 */
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }

        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
        }

        /* 次要按钮 - 灰色系 */
        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
        }

        /* 轮廓按钮 */
        .btn-outline {
            background: white;
            border: 1px solid #d1d5db;
            color: #374151;
        }

        .btn-outline:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        /* 文本按钮 */
        .btn-text {
            background: transparent;
            color: #6b7280;
            box-shadow: none;
            padding: 0.5rem 1rem;
        }

        .btn-text:hover {
            background: #f3f4f6;
            color: #374151;
            transform: none;
            box-shadow: none;
        }

        /* 优化的模态框样式 */
        .modal {
            backdrop-filter: blur(10px);
            animation: modalFadeIn 0.3s ease-out;
        }

        .modal-content {
            animation: modalSlideIn 0.3s ease-out;
            transform-origin: center;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(10px);
            }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.95) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        /* 模态框提示样式 */
        .modal-hint {
            animation: hintSlideDown 0.3s ease-out;
        }

        @keyframes hintSlideDown {
            from {
                opacity: 0;
                transform: translate(-50%, -30px);
            }
            to {
                opacity: 1;
                transform: translate(-50%, 0);
            }
        }

        /* 模态框关闭按钮样式 */
        .modal-content .mdi-close {
            transition: all 0.2s ease;
        }

        .modal-content .mdi-close:hover {
            transform: scale(1.1);
            color: #ef4444;
        }

        /* 表单输入框聚焦效果 */
        .modal input:focus,
        .modal textarea:focus,
        .modal select:focus {
            ring-width: 2px;
            ring-color: #3b82f6;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 未保存更改确认弹窗样式 */
        .fixed.inset-0.bg-gray-900 {
            backdrop-filter: blur(8px);
            animation: confirmFadeIn 0.2s ease-out;
        }

        @keyframes confirmFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(8px);
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- 顶部导航 -->
    <nav class="bg-white shadow-sm border-b border-gray-200 fixed top-0 left-0 right-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-xl font-semibold text-gray-900">📚 书签管理后台</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <a href="/" class="btn btn-text">
                        <i class="mdi mdi-home mr-2"></i>
                        返回前台
                    </a>
                    <button onclick="refreshData()" class="btn btn-primary">
                        <i class="mdi mdi-refresh mr-2"></i>
                        刷新数据
                    </button>
                    <button onclick="logout()" class="btn btn-secondary">
                        <i class="mdi mdi-logout mr-2"></i>
                        退出登录
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 页面主体容器 -->
    <div class="pt-16">
        <!-- 左侧边栏 -->
        <aside class="w-64 bg-white shadow-sm border-r border-gray-200 fixed left-0 top-16 bottom-0 overflow-y-auto z-40">
            <nav class="p-4">
                <div class="space-y-2">
                    <button onclick="showDashboard()" class="sidebar-item w-full text-left px-4 py-3 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 active">
                        <i class="mdi mdi-view-dashboard mr-3 text-lg"></i>
                        仪表盘
                    </button>
                    <button onclick="showCategories()" class="sidebar-item w-full text-left px-4 py-3 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                        <i class="mdi mdi-folder-multiple mr-3 text-lg"></i>
                        分类管理
                    </button>
                    <button onclick="showBookmarks()" class="sidebar-item w-full text-left px-4 py-3 rounded-lg text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200">
                        <i class="mdi mdi-bookmark mr-3 text-lg"></i>
                        书签管理
                    </button>



                </div>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="ml-64 h-screen bg-gray-50 overflow-hidden">
            <div class="h-full flex flex-col">
                <div class="flex-1 overflow-y-auto">
                    <div class="p-6">
                        <div class="max-w-7xl mx-auto">
                    <!-- 仪表盘 -->
                    <div id="dashboard" class="content-section dashboard-layout">
                        <!-- 仪表板标题 -->
                        <div class="dashboard-header">
                            <h1 class="dashboard-title">📊 数据仪表板</h1>
                            <p class="dashboard-subtitle">书签管理系统数据概览与统计</p>
                        </div>

                        <!-- 统计卡片网格 -->
                        <div class="dashboard-grid">
                            <!-- 分类总数卡片 -->
                            <div class="stats-card" style="--card-color: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                                <div class="p-8">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="stats-label">主分类总数</div>
                                            <div class="stats-number" id="stats-categories">-</div>
                                            <div class="text-sm text-gray-500 mt-2">
                                                <i class="mdi mdi-trending-up mr-1"></i>
                                                管理您的分类结构
                                            </div>
                                        </div>
                                        <div class="stats-icon bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                                            <i class="mdi mdi-folder-multiple"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 子分类总数卡片 -->
                            <div class="stats-card" style="--card-color: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                                <div class="p-8">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="stats-label">子分类总数</div>
                                            <div class="stats-number" id="stats-subcategories">-</div>
                                            <div class="text-sm text-gray-500 mt-2">
                                                <i class="mdi mdi-file-tree mr-1"></i>
                                                细分您的内容
                                            </div>
                                        </div>
                                        <div class="stats-icon bg-gradient-to-br from-pink-500 to-red-500 text-white">
                                            <i class="mdi mdi-folder-outline"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 书签总数卡片 -->
                            <div class="stats-card" style="--card-color: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                                <div class="p-8">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="stats-label">书签总数</div>
                                            <div class="stats-number" id="stats-bookmarks">-</div>
                                            <div class="text-sm text-gray-500 mt-2">
                                                <i class="mdi mdi-link-variant mr-1"></i>
                                                收藏的网站链接
                                            </div>
                                        </div>
                                        <div class="stats-icon bg-gradient-to-br from-blue-500 to-cyan-500 text-white">
                                            <i class="mdi mdi-bookmark-multiple"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 总点击数卡片 -->
                            <div class="stats-card" style="--card-color: linear-gradient(135deg, #fa709a 0%, #fee140 100%);">
                                <div class="p-8">
                                    <div class="flex items-center justify-between">
                                        <div class="flex-1">
                                            <div class="stats-label">总点击数</div>
                                            <div class="stats-number" id="stats-clicks">-</div>
                                            <div class="text-sm text-gray-500 mt-2">
                                                <i class="mdi mdi-chart-line mr-1"></i>
                                                用户访问统计
                                            </div>
                                        </div>
                                        <div class="stats-icon bg-gradient-to-br from-orange-500 to-yellow-500 text-white">
                                            <i class="mdi mdi-eye-outline"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 快速操作区域 -->
                        <div class="mt-12 text-center">
                            <h3 class="text-lg font-semibold text-gray-800 mb-6">快速操作</h3>
                            <div class="flex flex-wrap justify-center gap-4">
                                <button onclick="showCategories()" class="btn btn-outline">
                                    <i class="mdi mdi-folder-multiple mr-2 text-blue-600"></i>
                                    管理分类
                                </button>
                                <button onclick="showBookmarks()" class="btn btn-outline">
                                    <i class="mdi mdi-bookmark mr-2 text-purple-600"></i>
                                    管理书签
                                </button>
                                <button onclick="showBatchAddBookmarkModal()" class="btn btn-purple">
                                    <i class="mdi mdi-plus-box-multiple mr-2"></i>
                                    批量添加书签
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 分类管理 -->
                    <div id="categories" class="content-section hidden">
                        <div class="mb-8">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900">分类管理</h1>
                                    <p class="text-gray-600 mt-2">管理主分类和子分类，组织您的书签结构</p>
                                </div>
                                <div class="flex flex-wrap gap-3">
                                    <button onclick="showAddCategoryModal()" class="btn btn-primary">
                                        <i class="mdi mdi-plus mr-2"></i>
                                        添加主分类
                                    </button>
                                    <button onclick="showAddSubcategoryModal()" class="btn btn-success">
                                        <i class="mdi mdi-plus mr-2"></i>
                                        添加子分类
                                    </button>
                                    <button onclick="expandAllCategories()" class="btn btn-secondary btn-sm">
                                        <i class="mdi mdi-unfold-more-horizontal mr-1"></i>
                                        展开全部
                                    </button>
                                    <button onclick="collapseAllCategories()" class="btn btn-secondary btn-sm">
                                        <i class="mdi mdi-unfold-less-horizontal mr-1"></i>
                                        折叠全部
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- 分类树形列表 -->
                        <div>
                            <div class="flex items-center mb-6">
                                <div class="w-1 h-6 bg-blue-500 rounded-full mr-3"></div>
                                <h2 class="text-xl font-semibold text-gray-800">分类结构</h2>
                                <span class="ml-3 text-sm text-gray-500">点击主分类可展开/折叠子分类</span>
                            </div>
                            <div class="bg-white shadow-sm overflow-hidden rounded-xl border border-gray-200">
                                <div class="table-container">
                                    <table class="min-w-full">
                                        <thead class="bg-gray-50 border-b border-gray-200">
                                            <tr>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-2/5">分类结构</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/5">统计</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-16">排序</th>
                                                <th class="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-32">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="categories-tree" class="bg-white">
                                            <!-- 数据将通过JavaScript填充 -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 书签管理 -->
                    <div id="bookmarks" class="content-section hidden">
                        <div class="mb-8">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h1 class="text-3xl font-bold text-gray-900">书签管理</h1>
                                    <p class="text-gray-600 mt-2">管理所有书签链接，编辑内容和查看统计</p>
                                </div>
                                <button onclick="showBatchAddBookmarkModal()" class="btn btn-purple">
                                    <i class="mdi mdi-plus-box-multiple mr-2"></i>
                                    批量添加书签
                                </button>
                            </div>
                        </div>

                        <!-- 搜索和筛选区域 -->
                        <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0 md:space-x-4">
                                <!-- 搜索框 -->
                                <div class="flex-1 max-w-md">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="mdi mdi-magnify text-gray-400"></i>
                                        </div>
                                        <input type="text" id="bookmark-search"
                                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="搜索书签标题、描述或网址..."
                                               oninput="handleBookmarkSearch()"
                                               onkeyup="if(event.key==='Escape') clearBookmarkSearch()">
                                        <button onclick="clearBookmarkSearch()"
                                                id="clear-search-btn"
                                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 hidden">
                                            <i class="mdi mdi-close"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- 每页显示数量选择 -->
                                <div class="flex items-center space-x-2">
                                    <label class="text-sm text-gray-600">每页显示:</label>
                                    <select id="page-size-select"
                                            class="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                                            onchange="changePageSize()">
                                        <option value="20" selected>20条</option>
                                        <option value="35">35条</option>
                                        <option value="50">50条</option>
                                        <option value="75">75条</option>
                                        <option value="100">100条</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- 书签表格 -->
                        <div class="bg-white shadow-sm overflow-hidden rounded-xl border border-gray-200">
                            <div class="table-container">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-16">ID</th>
                                            <th class="px-4 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/4">标题</th>
                                            <th class="px-4 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/4">URL</th>
                                            <th class="px-4 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/5">所属分类</th>
                                            <th class="px-4 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-16">点击数</th>
                                            <th class="px-4 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-24">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bookmarks-table" class="bg-white divide-y divide-gray-100">
                                        <!-- 数据将通过JavaScript填充 -->
                                    </tbody>
                                </table>
                            </div>

                            <!-- 分页控件 -->
                            <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                                    <!-- 分页信息 -->
                                    <div>
                                        <p class="text-sm text-gray-700" id="pagination-info">
                                            显示第 <span class="font-medium">1</span> 到 <span class="font-medium">20</span> 条，共 <span class="font-medium">0</span> 条记录
                                        </p>
                                    </div>

                                    <!-- 分页按钮 -->
                                    <div class="flex justify-center sm:justify-end">
                                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" id="pagination-nav">
                                            <!-- 分页按钮将通过JavaScript生成 -->
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>






                </div>
            </div>
        </main>
    </div>

    <!-- 模态框容器 -->
    <div id="modal-container"></div>



    <script src="admin.js"></script>
</body>
</html>
