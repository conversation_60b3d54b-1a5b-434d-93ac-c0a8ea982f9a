// 全局变量
let currentData = {
    categories: [],
    subcategories: [],
    bookmarks: [],
    stats: {}
};

// 书签管理分页变量
let currentBookmarkPage = 1;
let bookmarkPageSize = 20;
let filteredBookmarks = [];
let bookmarkSearchTerm = '';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    // 首先验证用户登录状态
    const isAuthenticated = await checkAuthStatus();
    if (!isAuthenticated) {
        // 如果未登录，重定向到登录页面
        window.location.href = '/login';
        return;
    }

    // 用户已登录，加载数据
    loadStats();
    Promise.all([loadCategories(), loadSubcategories(), loadBookmarks()]).then(() => {
        // 数据加载完成后更新表格显示
        updateCategoriesTable();
    });
});

// 显示不同的内容区域
function showSection(sectionId) {
    // 隐藏所有内容区域
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.add('hidden');
    });
    
    // 移除所有侧边栏激活状态
    document.querySelectorAll('.sidebar-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // 显示指定区域
    document.getElementById(sectionId).classList.remove('hidden');
    
    // 激活对应的侧边栏项
    event.target.classList.add('active');
}

function showDashboard() {
    showSection('dashboard');
    loadStats();
    // 仪表板不需要滚动，调整主内容区样式
    const mainContent = document.querySelector('main .flex-1');
    if (mainContent) {
        mainContent.style.overflow = 'hidden';
    }
}

function showCategories() {
    showSection('categories');
    loadCategories();
    // 其他页面需要滚动
    const mainContent = document.querySelector('main .flex-1');
    if (mainContent) {
        mainContent.style.overflow = 'auto';
    }
}

function showBookmarks() {
    showSection('bookmarks');
    // 重置搜索和分页状态
    currentBookmarkPage = 1;
    bookmarkSearchTerm = '';
    filteredBookmarks = []; // 初始化过滤数组
    const searchInput = document.getElementById('bookmark-search');
    const clearBtn = document.getElementById('clear-search-btn');
    if (searchInput) searchInput.value = '';
    if (clearBtn) clearBtn.classList.add('hidden');

    // 加载书签数据
    loadBookmarks();

    // 其他页面需要滚动
    const mainContent = document.querySelector('main .flex-1');
    if (mainContent) {
        mainContent.style.overflow = 'auto';
    }
}







// 加载统计数据
async function loadStats() {
    try {
        const response = await fetch('/admin/api/stats', {
            headers: {
                'Authorization': `Bearer ${getSessionId()}`
            }
        });
        const result = await response.json();

        if (result.success) {
            currentData.stats = result.data;
            updateStatsDisplay();
        }
    } catch (error) {
        console.error('加载统计数据失败:', error);
        showNotification('加载统计数据失败', 'error');
    }
}

function updateStatsDisplay() {
    const stats = currentData.stats;
    document.getElementById('stats-categories').textContent = stats.categories || 0;
    document.getElementById('stats-subcategories').textContent = stats.subcategories || 0;
    document.getElementById('stats-bookmarks').textContent = stats.bookmarks || 0;
    document.getElementById('stats-clicks').textContent = stats.totalClicks || 0;
}

// 加载分类数据
async function loadCategories() {
    try {
        const response = await fetch('/admin/api/categories', {
            headers: {
                'Authorization': `Bearer ${getSessionId()}`
            }
        });
        const result = await response.json();

        if (result.success) {
            currentData.categories = result.data;
            updateCategoriesTable();
        }
        return result.success;
    } catch (error) {
        console.error('加载分类数据失败:', error);
        showNotification('加载分类数据失败', 'error');
        return false;
    }
}

function updateCategoriesTable() {
    const tbody = document.getElementById('categories-tree');
    tbody.innerHTML = '';

    // 按排序顺序显示主分类
    const sortedCategories = [...currentData.categories].sort((a, b) => (a.category_order || 0) - (b.category_order || 0));

    sortedCategories.forEach(category => {
        // 获取该分类下的子分类
        const subcategories = currentData.subcategories
            .filter(sub => sub.category_id === category.category_id)
            .sort((a, b) => (a.subcategory_order || 0) - (b.subcategory_order || 0));

        // 创建主分类行
        const categoryRow = document.createElement('tr');
        categoryRow.className = 'category-row';
        categoryRow.setAttribute('data-category-id', category.category_id);
        categoryRow.onclick = () => toggleCategory(category.category_id);

        categoryRow.innerHTML = `
            <td class="px-6 py-4">
                <div class="flex items-center">
                    <i class="mdi mdi-chevron-right expand-icon text-gray-400 mr-2" id="icon-${category.category_id}"></i>
                    <i class="mdi mdi-folder text-blue-500 mr-2"></i>
                    <span class="category-title">${category.category_title}</span>
                </div>
            </td>
            <td class="px-6 py-4">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    ${subcategories.length} 个子分类
                </span>
            </td>
            <td class="px-6 py-4 text-sm text-gray-900">${category.category_order || 0}</td>
            <td class="px-6 py-4">
                <div class="flex space-x-2">
                    <button onclick="event.stopPropagation(); editCategory(${category.category_id})" class="btn btn-warning btn-sm">
                        <i class="mdi mdi-pencil mr-1"></i>
                        编辑
                    </button>
                    <button onclick="event.stopPropagation(); deleteCategory(${category.category_id})" class="btn btn-danger btn-sm">
                        <i class="mdi mdi-delete mr-1"></i>
                        删除
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(categoryRow);

        // 创建子分类行
        subcategories.forEach(subcategory => {
            // 计算该子分类下的书签数量
            const bookmarkCount = currentData.bookmarks.filter(bookmark => bookmark.subcategory_id === subcategory.subcategory_id).length;

            const subcategoryRow = document.createElement('tr');
            subcategoryRow.className = 'subcategory-row';
            subcategoryRow.setAttribute('data-category-id', category.category_id);
            subcategoryRow.setAttribute('data-subcategory-id', subcategory.subcategory_id);

            subcategoryRow.innerHTML = `
                <td class="px-6 py-3">
                    <div class="flex items-center">
                        <div class="w-6 mr-2"></div>
                        <i class="mdi mdi-folder-outline text-green-500 mr-2"></i>
                        <span class="subcategory-title">${subcategory.subcategory_title}</span>
                    </div>
                </td>
                <td class="px-6 py-3">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        ${bookmarkCount} 个书签
                    </span>
                </td>
                <td class="px-6 py-3 text-sm text-gray-900">${subcategory.subcategory_order || 0}</td>
                <td class="px-6 py-3">
                    <div class="flex space-x-2">
                        <button onclick="editSubcategory(${subcategory.subcategory_id})" class="btn btn-warning btn-sm">
                            <i class="mdi mdi-pencil mr-1"></i>
                            编辑
                        </button>
                        <button onclick="deleteSubcategory(${subcategory.subcategory_id})" class="btn btn-danger btn-sm">
                            <i class="mdi mdi-delete mr-1"></i>
                            删除
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(subcategoryRow);
        });
    });
}

// 切换分类展开/折叠状态
function toggleCategory(categoryId) {
    const categoryRow = document.querySelector(`tr.category-row[data-category-id="${categoryId}"]`);
    const subcategoryRows = document.querySelectorAll(`tr.subcategory-row[data-category-id="${categoryId}"]`);
    const expandIcon = document.getElementById(`icon-${categoryId}`);

    if (!categoryRow || !expandIcon) return;

    const isExpanded = categoryRow.classList.contains('expanded');

    if (isExpanded) {
        // 折叠
        categoryRow.classList.remove('expanded');
        expandIcon.classList.remove('expanded');
        subcategoryRows.forEach(row => {
            row.classList.remove('show');
        });
    } else {
        // 展开
        categoryRow.classList.add('expanded');
        expandIcon.classList.add('expanded');
        subcategoryRows.forEach(row => {
            row.classList.add('show');
        });
    }
}

// 展开所有分类
function expandAllCategories() {
    currentData.categories.forEach(category => {
        const categoryRow = document.querySelector(`tr.category-row[data-category-id="${category.category_id}"]`);
        const subcategoryRows = document.querySelectorAll(`tr.subcategory-row[data-category-id="${category.category_id}"]`);
        const expandIcon = document.getElementById(`icon-${category.category_id}`);

        if (categoryRow && expandIcon) {
            categoryRow.classList.add('expanded');
            expandIcon.classList.add('expanded');
            subcategoryRows.forEach(row => {
                row.classList.add('show');
            });
        }
    });
}

// 折叠所有分类
function collapseAllCategories() {
    currentData.categories.forEach(category => {
        const categoryRow = document.querySelector(`tr.category-row[data-category-id="${category.category_id}"]`);
        const subcategoryRows = document.querySelectorAll(`tr.subcategory-row[data-category-id="${category.category_id}"]`);
        const expandIcon = document.getElementById(`icon-${category.category_id}`);

        if (categoryRow && expandIcon) {
            categoryRow.classList.remove('expanded');
            expandIcon.classList.remove('expanded');
            subcategoryRows.forEach(row => {
                row.classList.remove('show');
            });
        }
    });
}

// 加载子分类数据
async function loadSubcategories() {
    try {
        const response = await fetch('/admin/api/subcategories', {
            headers: {
                'Authorization': `Bearer ${getSessionId()}`
            }
        });
        const result = await response.json();

        if (result.success) {
            currentData.subcategories = result.data;
            // 子分类表格已合并到分类表格中，不需要单独更新
        }
        return result.success;
    } catch (error) {
        console.error('加载子分类数据失败:', error);
        showNotification('加载子分类数据失败', 'error');
        return false;
    }
}

// 更新子分类表格函数已合并到updateCategoriesTable中

// 加载书签数据
async function loadBookmarks() {
    try {
        // 获取所有书签数据，设置一个较大的limit值
        const response = await fetch('/admin/api/bookmarks?limit=10000', {
            headers: {
                'Authorization': `Bearer ${getSessionId()}`
            }
        });
        const result = await response.json();

        if (result.success) {
            currentData.bookmarks = result.data;
            updateBookmarksTable();
        }
        return result.success;
    } catch (error) {
        console.error('加载书签数据失败:', error);
        showNotification('加载书签数据失败', 'error');
        return false;
    }
}

function updateBookmarksTable() {
    // 应用搜索过滤
    applyBookmarkFilter();

    const tbody = document.getElementById('bookmarks-table');
    tbody.innerHTML = '';

    // 计算分页
    const totalItems = filteredBookmarks.length;
    const totalPages = Math.ceil(totalItems / bookmarkPageSize);
    const startIndex = (currentBookmarkPage - 1) * bookmarkPageSize;
    const endIndex = Math.min(startIndex + bookmarkPageSize, totalItems);
    const pageBookmarks = filteredBookmarks.slice(startIndex, endIndex);

    // 显示书签
    if (pageBookmarks.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="6" class="px-4 py-8 text-center text-gray-500">
                <i class="mdi mdi-bookmark-outline text-4xl mb-2 block"></i>
                ${bookmarkSearchTerm ? '没有找到匹配的书签' : '暂无书签数据'}
            </td>
        `;
        tbody.appendChild(row);
    } else {
        pageBookmarks.forEach(bookmark => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">${bookmark.bookmark_id}</td>
                <td class="px-4 py-4 text-sm text-gray-900">
                    <div class="max-w-xs truncate" title="${bookmark.bookmark_title}">
                        ${highlightSearchTerm(bookmark.bookmark_title, bookmarkSearchTerm)}
                    </div>
                </td>
                <td class="px-4 py-4 text-sm text-blue-600">
                    <a href="${bookmark.bookmark_url}" target="_blank" class="hover:underline max-w-xs block truncate" title="${bookmark.bookmark_url}">
                        ${highlightSearchTerm(bookmark.bookmark_url.length > 50 ? bookmark.bookmark_url.substring(0, 50) + '...' : bookmark.bookmark_url, bookmarkSearchTerm)}
                    </a>
                </td>
                <td class="px-4 py-3 text-sm text-gray-900">
                    <div class="flex flex-col space-y-1">
                        <div class="flex items-center">
                            <i class="mdi mdi-folder text-blue-500 mr-1 text-sm"></i>
                            <span class="text-xs text-gray-600 truncate" title="${bookmark.category_title || '未知分类'}">
                                ${bookmark.category_title || '未知分类'}
                            </span>
                        </div>
                        <div class="flex items-center">
                            <i class="mdi mdi-folder-outline text-green-500 mr-1 text-sm"></i>
                            <span class="text-xs text-gray-800 truncate font-medium" title="${bookmark.subcategory_title || '未知子分类'}">
                                ${bookmark.subcategory_title || '未知子分类'}
                            </span>
                        </div>
                    </div>
                </td>
                <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900 text-center">${bookmark.click_count || 0}</td>
                <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                        <button onclick="editBookmark(${bookmark.bookmark_id})" class="btn btn-warning btn-sm">
                            <i class="mdi mdi-pencil mr-1"></i>
                            编辑
                        </button>
                        <button onclick="deleteBookmark(${bookmark.bookmark_id})" class="btn btn-danger btn-sm">
                            <i class="mdi mdi-delete mr-1"></i>
                            删除
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    // 更新分页信息
    updateBookmarkPagination(totalItems, startIndex + 1, endIndex, totalPages);
}

// 应用书签搜索过滤
function applyBookmarkFilter() {
    if (!bookmarkSearchTerm.trim()) {
        filteredBookmarks = [...currentData.bookmarks];
    } else {
        const searchLower = bookmarkSearchTerm.toLowerCase();
        filteredBookmarks = currentData.bookmarks.filter(bookmark => {
            return bookmark.bookmark_title.toLowerCase().includes(searchLower) ||
                   (bookmark.bookmark_description && bookmark.bookmark_description.toLowerCase().includes(searchLower)) ||
                   bookmark.bookmark_url.toLowerCase().includes(searchLower) ||
                   (bookmark.category_title && bookmark.category_title.toLowerCase().includes(searchLower)) ||
                   (bookmark.subcategory_title && bookmark.subcategory_title.toLowerCase().includes(searchLower));
        });
    }
}

// 高亮搜索关键词
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm.trim()) {
        return text;
    }

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
}

// 处理书签搜索
function handleBookmarkSearch() {
    const searchInput = document.getElementById('bookmark-search');
    const clearBtn = document.getElementById('clear-search-btn');

    bookmarkSearchTerm = searchInput.value.trim();

    // 显示/隐藏清除按钮
    if (bookmarkSearchTerm) {
        clearBtn.classList.remove('hidden');
    } else {
        clearBtn.classList.add('hidden');
    }

    // 延迟搜索，避免频繁更新
    clearTimeout(window.bookmarkSearchTimeout);
    window.bookmarkSearchTimeout = setTimeout(() => {
        updateBookmarksTable();
    }, 300);
}

// 清除书签搜索
function clearBookmarkSearch() {
    const searchInput = document.getElementById('bookmark-search');
    const clearBtn = document.getElementById('clear-search-btn');

    searchInput.value = '';
    bookmarkSearchTerm = '';
    clearBtn.classList.add('hidden');

    updateBookmarksTable();
}

// 更新分页信息和控件
function updateBookmarkPagination(totalItems, startIndex, endIndex, totalPages) {
    // 更新分页信息文本
    const paginationInfo = document.getElementById('pagination-info');
    if (paginationInfo) {
        paginationInfo.innerHTML = `
            显示第 <span class="font-medium">${startIndex}</span> 到 <span class="font-medium">${endIndex}</span> 条，
            共 <span class="font-medium">${totalItems}</span> 条记录
            ${bookmarkSearchTerm ? `<span class="text-blue-600">(搜索: "${bookmarkSearchTerm}")</span>` : ''}
        `;
    }

    // 生成分页按钮
    generatePaginationButtons(totalPages);
}

// 生成分页按钮
function generatePaginationButtons(totalPages) {
    const paginationNav = document.getElementById('pagination-nav');
    if (!paginationNav) return;

    paginationNav.innerHTML = '';

    if (totalPages <= 1) {
        // 如果只有一页或没有数据，显示禁用的按钮
        const prevBtn = createPaginationButton('<i class="mdi mdi-chevron-left mr-1"></i>上一页', 1, true);
        prevBtn.classList.add('rounded-l-md');
        paginationNav.appendChild(prevBtn);

        const pageInfo = document.createElement('span');
        pageInfo.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
        pageInfo.textContent = totalPages === 0 ? '0 / 0' : '1 / 1';
        paginationNav.appendChild(pageInfo);

        const nextBtn = createPaginationButton('下一页<i class="mdi mdi-chevron-right ml-1"></i>', 1, true);
        nextBtn.classList.add('rounded-r-md');
        paginationNav.appendChild(nextBtn);
        return;
    }

    // 上一页按钮
    const prevBtn = createPaginationButton('<i class="mdi mdi-chevron-left mr-1"></i>上一页', currentBookmarkPage - 1, currentBookmarkPage <= 1);
    prevBtn.classList.add('rounded-l-md');
    paginationNav.appendChild(prevBtn);

    // 页码信息
    const pageInfo = document.createElement('span');
    pageInfo.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
    pageInfo.textContent = `${currentBookmarkPage} / ${totalPages}`;
    paginationNav.appendChild(pageInfo);

    // 下一页按钮
    const nextBtn = createPaginationButton('下一页<i class="mdi mdi-chevron-right ml-1"></i>', currentBookmarkPage + 1, currentBookmarkPage >= totalPages);
    nextBtn.classList.add('rounded-r-md');
    paginationNav.appendChild(nextBtn);
}

// 创建分页按钮
function createPaginationButton(text, page, disabled = false, active = false) {
    const button = document.createElement('button');
    button.innerHTML = text; // 使用innerHTML支持图标
    button.onclick = (e) => {
        e.preventDefault();
        e.stopPropagation();
        if (!disabled) {
            goToPage(page);
        }
    };

    let classes = 'relative inline-flex items-center px-4 py-2 border text-sm font-medium transition-colors';

    if (disabled) {
        classes += ' border-gray-300 text-gray-300 cursor-not-allowed bg-gray-50';
    } else if (active) {
        classes += ' border-blue-500 bg-blue-50 text-blue-600 z-10';
    } else {
        classes += ' border-gray-300 text-gray-700 bg-white hover:bg-gray-50';
    }

    button.className = classes;
    return button;
}

// 创建分页省略号
function createPaginationEllipsis() {
    const span = document.createElement('span');
    span.textContent = '...';
    span.className = 'relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700';
    return span;
}

// 跳转到指定页
function goToPage(page) {
    const totalPages = Math.ceil(filteredBookmarks.length / bookmarkPageSize);

    if (page < 1 || page > totalPages) return;

    currentBookmarkPage = page;
    updateBookmarksTable();
}

// 改变每页显示数量
function changePageSize() {
    const select = document.getElementById('page-size-select');
    bookmarkPageSize = parseInt(select.value);
    currentBookmarkPage = 1; // 重置到第一页
    updateBookmarksTable();
}

// 刷新所有数据
function refreshData() {
    loadStats();
    Promise.all([loadCategories(), loadSubcategories(), loadBookmarks()]).then(() => {
        updateCategoriesTable();
        showNotification('数据已刷新', 'success');
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// 显示模态框
function showModal(title, content, widthClass = 'w-96', options = {}) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full modal z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border ${widthClass} shadow-lg rounded-md bg-white modal-content">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                    <button onclick="handleModalClose()" class="text-gray-400 hover:text-gray-600 transition-colors" title="关闭">
                        <i class="mdi mdi-close text-xl"></i>
                    </button>
                </div>
                ${content}
            </div>
        </div>
    `;

    document.getElementById('modal-container').appendChild(modal);

    // 存储模态框选项
    window.currentModalOptions = options;

    // 优化的点击处理 - 不再自动关闭，而是显示提示
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            handleModalBackgroundClick();
        }
    });

    // 监听ESC键
    document.addEventListener('keydown', handleModalEscKey);
}

function closeModal() {
    const modalContainer = document.getElementById('modal-container');
    modalContainer.innerHTML = '';

    // 清理事件监听器
    document.removeEventListener('keydown', handleModalEscKey);
    window.currentModalOptions = null;
}

// 处理模态框关闭
function handleModalClose() {
    // 检查是否有未保存的内容
    if (hasUnsavedChanges()) {
        showUnsavedChangesConfirm(() => {
            closeModal();
        });
    } else {
        closeModal();
    }
}

// 处理点击模态框背景
function handleModalBackgroundClick() {
    // 显示友好提示而不是直接关闭
    showModalClickHint();
}

// 处理ESC键
function handleModalEscKey(e) {
    if (e.key === 'Escape') {
        e.preventDefault();
        handleModalClose();
    }
}

// 检查是否有未保存的更改
function hasUnsavedChanges() {
    const modal = document.querySelector('.modal');
    if (!modal) return false;

    // 检查表单输入是否有内容
    const inputs = modal.querySelectorAll('input[type="text"], input[type="url"], textarea, select');
    for (let input of inputs) {
        if (input.value.trim() !== '' && input.value !== input.defaultValue) {
            return true;
        }
    }
    return false;
}

// 显示未保存更改确认
function showUnsavedChangesConfirm(onConfirm) {
    const confirmModal = document.createElement('div');
    confirmModal.className = 'fixed inset-0 bg-gray-900 bg-opacity-75 overflow-y-auto h-full w-full z-60';
    confirmModal.innerHTML = `
        <div class="relative top-1/2 transform -translate-y-1/2 mx-auto p-6 border w-96 shadow-xl rounded-lg bg-white">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                    <i class="mdi mdi-alert text-yellow-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">确认关闭</h3>
                <p class="text-sm text-gray-500 mb-6">您有未保存的更改，确定要关闭吗？</p>
                <div class="flex justify-center space-x-3">
                    <button onclick="closeUnsavedChangesConfirm()" class="btn btn-outline">
                        继续编辑
                    </button>
                    <button onclick="confirmCloseModal()" class="btn btn-danger">
                        放弃更改
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(confirmModal);

    // 存储确认回调
    window.currentCloseConfirmAction = onConfirm;
}

// 关闭未保存更改确认
function closeUnsavedChangesConfirm() {
    const confirmModal = document.querySelector('.fixed.inset-0.bg-gray-900');
    if (confirmModal) {
        confirmModal.remove();
    }
    window.currentCloseConfirmAction = null;
}

// 确认关闭模态框
function confirmCloseModal() {
    closeUnsavedChangesConfirm();
    if (window.currentCloseConfirmAction) {
        window.currentCloseConfirmAction();
    }
}

// 显示点击提示
function showModalClickHint() {
    // 避免重复显示提示
    if (document.querySelector('.modal-hint')) return;

    const hint = document.createElement('div');
    hint.className = 'modal-hint fixed top-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-70 transition-all duration-300';
    hint.innerHTML = `
        <div class="flex items-center space-x-2">
            <i class="mdi mdi-information text-sm"></i>
            <span class="text-sm">点击右上角 ✕ 或按 ESC 键关闭窗口</span>
        </div>
    `;

    document.body.appendChild(hint);

    // 3秒后自动消失
    setTimeout(() => {
        if (hint.parentNode) {
            hint.style.opacity = '0';
            hint.style.transform = 'translate(-50%, -20px)';
            setTimeout(() => hint.remove(), 300);
        }
    }, 3000);
}

// 显示自定义确认弹窗
function showConfirmModal(title, message, onConfirm, confirmText = '确认删除', confirmClass = 'bg-red-500 hover:bg-red-600') {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full confirm-modal z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white confirm-modal-content">
            <div class="mt-3">
                <div class="flex justify-between items-start mb-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mr-4">
                            <i class="mdi mdi-alert-circle text-red-600 text-2xl warning-icon"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">${title}</h3>
                        </div>
                    </div>
                    <button onclick="closeConfirmModal()" class="text-gray-400 hover:text-gray-600 transition-colors" title="取消">
                        <i class="mdi mdi-close text-xl"></i>
                    </button>
                </div>
                <div class="ml-16">
                    <div class="text-sm text-gray-500 mb-6">${message}</div>
                    <div class="flex justify-end space-x-3">
                        <button onclick="closeConfirmModal()" class="btn btn-outline">
                            取消
                        </button>
                        <button onclick="confirmAction()" class="btn btn-danger">
                            ${confirmText}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').appendChild(modal);

    // 存储确认回调函数
    window.currentConfirmAction = onConfirm;

    // 优化确认弹窗的点击处理 - 删除操作比较危险，保持原有的点击外部关闭行为
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeConfirmModal();
        }
    });

    // ESC键关闭
    const handleEsc = (e) => {
        if (e.key === 'Escape') {
            closeConfirmModal();
            document.removeEventListener('keydown', handleEsc);
        }
    };
    document.addEventListener('keydown', handleEsc);
}

// 关闭确认弹窗
function closeConfirmModal() {
    const modalContainer = document.getElementById('modal-container');
    modalContainer.innerHTML = '';
    window.currentConfirmAction = null;
}

// 执行确认操作
function confirmAction() {
    if (window.currentConfirmAction) {
        window.currentConfirmAction();
    }
    closeConfirmModal();
}

// 显示警告弹窗（不能删除时使用）
function showWarningModal(title, message) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full confirm-modal z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-lg bg-white confirm-modal-content">
            <div class="mt-3">
                <div class="flex items-center mb-4">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
                        <i class="mdi mdi-alert text-yellow-600 text-2xl warning-icon"></i>
                    </div>
                </div>
                <div class="text-center">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">${title}</h3>
                    <div class="text-sm text-gray-500 mb-6">${message}</div>
                    <div class="flex justify-center">
                        <button onclick="closeConfirmModal()" class="btn btn-primary">
                            我知道了
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modal-container').appendChild(modal);

    // 点击背景关闭弹窗
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            closeConfirmModal();
        }
    });
}

// 分类相关操作
function showAddCategoryModal() {
    // 计算当前最大排序值
    const maxOrder = currentData.categories.length > 0
        ? Math.max(...currentData.categories.map(cat => cat.category_order || 0))
        : 0;
    const nextOrder = maxOrder + 1;

    const content = `
        <form onsubmit="addCategory(event)">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">分类标题</label>
                <input type="text" id="category-title" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       oninput="validateCategoryTitle(this.value, null)"
                       placeholder="请输入分类标题">
                <div id="category-title-error" class="text-red-500 text-xs mt-1 hidden"></div>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    排序
                    <span class="text-xs text-gray-500">(当前建议值: ${nextOrder})</span>
                </label>
                <input type="number" id="category-order" value="${nextOrder}" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">数字越小排序越靠前，可以修改为其他数值</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="handleModalClose()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-primary">添加</button>
            </div>
        </form>
    `;
    showModal('添加主分类', content, 'w-96', { hasForm: true });

    // 自动聚焦到标题输入框
    setTimeout(() => {
        const titleInput = document.getElementById('category-title');
        if (titleInput) titleInput.focus();
    }, 100);
}

// 验证分类标题
function validateCategoryTitle(title, excludeCategoryId = null) {
    const errorDiv = document.getElementById('category-title-error');
    if (!errorDiv) return true;

    const trimmedTitle = title.trim();

    if (!trimmedTitle) {
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
        return true;
    }

    const existingCategory = currentData.categories.find(cat =>
        (excludeCategoryId === null || cat.category_id !== excludeCategoryId) &&
        cat.category_title.toLowerCase() === trimmedTitle.toLowerCase()
    );

    if (existingCategory) {
        errorDiv.textContent = `分类标题 "${trimmedTitle}" 已存在`;
        errorDiv.classList.remove('hidden');
        return false;
    } else {
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
        return true;
    }
}

// 验证子分类标题
function validateSubcategoryTitle(title, categoryId, excludeSubcategoryId = null) {
    const errorDiv = document.getElementById('subcategory-title-error');
    if (!errorDiv || !categoryId) return true;

    const trimmedTitle = title.trim();

    if (!trimmedTitle) {
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
        return true;
    }

    const existingSubcategory = currentData.subcategories.find(sub =>
        (excludeSubcategoryId === null || sub.subcategory_id !== excludeSubcategoryId) &&
        sub.category_id === parseInt(categoryId) &&
        sub.subcategory_title.toLowerCase() === trimmedTitle.toLowerCase()
    );

    if (existingSubcategory) {
        const categoryName = currentData.categories.find(cat => cat.category_id === parseInt(categoryId))?.category_title || '该分类';
        errorDiv.textContent = `在 "${categoryName}" 下已存在子分类 "${trimmedTitle}"`;
        errorDiv.classList.remove('hidden');
        return false;
    } else {
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
        return true;
    }
}

async function addCategory(event) {
    event.preventDefault();

    const categoryTitle = document.getElementById('category-title').value.trim();

    // 检查标题是否为空
    if (!categoryTitle) {
        showNotification('分类标题不能为空', 'error');
        return;
    }

    // 检查是否存在相同标题的分类
    const existingCategory = currentData.categories.find(cat =>
        cat.category_title.toLowerCase() === categoryTitle.toLowerCase()
    );

    if (existingCategory) {
        showNotification(`分类标题 "${categoryTitle}" 已存在，请使用其他名称`, 'error');
        return;
    }

    const data = {
        category_title: categoryTitle,
        category_icon: 'mdi-folder',
        category_color: 'text-gray-600',
        category_order: parseInt(document.getElementById('category-order').value)
    };

    try {
        const response = await fetch('/admin/api/categories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            loadCategories();
            loadStats();
            showNotification('分类添加成功', 'success');
        } else {
            showNotification(result.message || '添加失败', 'error');
        }
    } catch (error) {
        console.error('添加分类失败:', error);
        showNotification('添加分类失败', 'error');
    }
}

function editCategory(categoryId) {
    const category = currentData.categories.find(c => c.category_id === categoryId);
    if (!category) return;

    const content = `
        <form onsubmit="updateCategory(event, ${categoryId})">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">分类标题</label>
                <input type="text" id="edit-category-title" value="${category.category_title}" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">排序</label>
                <input type="number" id="edit-category-order" value="${category.category_order || 0}" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">数字越小排序越靠前</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="handleModalClose()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-warning">更新</button>
            </div>
        </form>
    `;
    showModal('编辑主分类', content, 'w-96', { hasForm: true });

    // 自动聚焦到标题输入框
    setTimeout(() => {
        const titleInput = document.getElementById('edit-category-title');
        if (titleInput) {
            titleInput.focus();
            titleInput.select(); // 选中现有文本便于编辑
        }
    }, 100);
}

async function updateCategory(event, categoryId) {
    event.preventDefault();

    const categoryTitle = document.getElementById('edit-category-title').value.trim();

    // 检查标题是否为空
    if (!categoryTitle) {
        showNotification('分类标题不能为空', 'error');
        return;
    }

    // 检查是否存在相同标题的其他分类（排除当前编辑的分类）
    const existingCategory = currentData.categories.find(cat =>
        cat.category_id !== categoryId &&
        cat.category_title.toLowerCase() === categoryTitle.toLowerCase()
    );

    if (existingCategory) {
        showNotification(`分类标题 "${categoryTitle}" 已存在，请使用其他名称`, 'error');
        return;
    }

    const data = {
        category_title: categoryTitle,
        category_icon: 'mdi-folder',
        category_color: 'text-gray-600',
        category_order: parseInt(document.getElementById('edit-category-order').value)
    };

    try {
        const response = await fetch(`/admin/api/categories/${categoryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            loadCategories();
            showNotification('分类更新成功', 'success');
        } else {
            showNotification(result.message || '更新失败', 'error');
        }
    } catch (error) {
        console.error('更新分类失败:', error);
        showNotification('更新分类失败', 'error');
    }
}

async function deleteCategory(categoryId) {
    const category = currentData.categories.find(cat => cat.category_id === categoryId);
    if (!category) {
        showNotification('分类不存在', 'error');
        return;
    }

    // 检查是否有子分类
    const subcategories = currentData.subcategories.filter(sub => sub.category_id === categoryId);

    if (subcategories.length > 0) {
        const subcategoryNames = subcategories.map(sub => sub.subcategory_title).join('、');
        showWarningModal(
            '无法删除分类',
            `分类 "${category.category_title}" 下还有 ${subcategories.length} 个子分类：<br><br><strong>${subcategoryNames}</strong><br><br>请先删除所有子分类后再删除主分类。`
        );
        return;
    }

    // 显示确认删除弹窗
    showConfirmModal(
        '删除分类确认',
        `确定要删除分类 "<strong>${category.category_title}</strong>" 吗？<br><br>此操作不可撤销。`,
        async () => {
            try {
                const response = await fetch(`/admin/api/categories/${categoryId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    // 重新加载数据
                    await Promise.all([loadCategories(), loadSubcategories()]);
                    updateCategoriesTable();
                    loadStats();
                    showNotification('分类删除成功', 'success');
                } else {
                    showNotification(result.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除分类失败:', error);
                showNotification('删除分类失败', 'error');
            }
        }
    );
}

// 子分类相关操作
function showAddSubcategoryModal() {
    const categoryOptions = currentData.categories.map(cat =>
        `<option value="${cat.category_id}">📂 ${cat.category_title}</option>`
    ).join('');

    // 计算当前最大排序值
    const maxOrder = currentData.subcategories.length > 0
        ? Math.max(...currentData.subcategories.map(sub => sub.subcategory_order || 0))
        : 0;
    const nextOrder = maxOrder + 1;

    const content = `
        <form onsubmit="addSubcategory(event)">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">所属分类</label>
                <select id="subcategory-category" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="updateSubcategoryOrder(); validateSubcategoryTitle(document.getElementById('subcategory-title').value, this.value, null)">
                    <option value="">请选择分类</option>
                    ${categoryOptions}
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">子分类标题</label>
                <input type="text" id="subcategory-title" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                       oninput="validateSubcategoryTitle(this.value, document.getElementById('subcategory-category').value, null)"
                       placeholder="请输入子分类标题">
                <div id="subcategory-title-error" class="text-red-500 text-xs mt-1 hidden"></div>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    排序
                    <span class="text-xs text-gray-500" id="subcategory-order-hint">(当前建议值: ${nextOrder})</span>
                </label>
                <input type="number" id="subcategory-order" value="${nextOrder}" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">数字越小排序越靠前，可以修改为其他数值</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="handleModalClose()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-success">添加</button>
            </div>
        </form>
    `;
    showModal('添加子分类', content, 'w-96', { hasForm: true });

    // 自动聚焦到分类选择框
    setTimeout(() => {
        const categorySelect = document.getElementById('subcategory-category');
        if (categorySelect) categorySelect.focus();
    }, 100);
}

// 动态更新子分类排序建议值
function updateSubcategoryOrder() {
    const selectedCategoryId = parseInt(document.getElementById('subcategory-category').value);
    if (!selectedCategoryId) return;

    // 计算该分类下子分类的最大排序值
    const categorySubcategories = currentData.subcategories.filter(sub => sub.category_id === selectedCategoryId);
    const maxOrder = categorySubcategories.length > 0
        ? Math.max(...categorySubcategories.map(sub => sub.subcategory_order || 0))
        : 0;
    const nextOrder = maxOrder + 1;

    // 更新排序输入框和提示
    const orderInput = document.getElementById('subcategory-order');
    const orderHint = document.getElementById('subcategory-order-hint');

    if (orderInput && orderHint) {
        orderInput.value = nextOrder;
        orderHint.textContent = `(该分类下建议值: ${nextOrder})`;
    }
}

async function addSubcategory(event) {
    event.preventDefault();

    const categoryId = parseInt(document.getElementById('subcategory-category').value);
    const subcategoryTitle = document.getElementById('subcategory-title').value.trim();

    // 检查是否选择了分类
    if (!categoryId) {
        showNotification('请选择所属分类', 'error');
        return;
    }

    // 检查标题是否为空
    if (!subcategoryTitle) {
        showNotification('子分类标题不能为空', 'error');
        return;
    }

    // 检查在同一主分类下是否存在相同标题的子分类
    const existingSubcategory = currentData.subcategories.find(sub =>
        sub.category_id === categoryId &&
        sub.subcategory_title.toLowerCase() === subcategoryTitle.toLowerCase()
    );

    if (existingSubcategory) {
        const categoryName = currentData.categories.find(cat => cat.category_id === categoryId)?.category_title || '该分类';
        showNotification(`在 "${categoryName}" 下已存在子分类 "${subcategoryTitle}"，请使用其他名称`, 'error');
        return;
    }

    const data = {
        category_id: categoryId,
        subcategory_title: subcategoryTitle,
        subcategory_icon: 'mdi-folder-outline',
        subcategory_color: 'text-gray-600',
        subcategory_order: parseInt(document.getElementById('subcategory-order').value)
    };

    try {
        const response = await fetch('/admin/api/subcategories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            await loadSubcategories();
            updateCategoriesTable(); // 更新分类树显示
            loadStats();
            showNotification('子分类添加成功', 'success');
        } else {
            showNotification(result.message || '添加失败', 'error');
        }
    } catch (error) {
        console.error('添加子分类失败:', error);
        showNotification('添加子分类失败', 'error');
    }
}

function editSubcategory(subcategoryId) {
    const subcategory = currentData.subcategories.find(s => s.subcategory_id === subcategoryId);
    if (!subcategory) return;

    const categoryOptions = currentData.categories.map(cat =>
        `<option value="${cat.category_id}" ${cat.category_id === subcategory.category_id ? 'selected' : ''}>📂 ${cat.category_title}</option>`
    ).join('');

    const content = `
        <form onsubmit="updateSubcategory(event, ${subcategoryId})">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">所属分类</label>
                <select id="edit-subcategory-category" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    ${categoryOptions}
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">子分类标题</label>
                <input type="text" id="edit-subcategory-title" value="${subcategory.subcategory_title}" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">排序</label>
                <input type="number" id="edit-subcategory-order" value="${subcategory.subcategory_order || 0}" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">数字越小排序越靠前</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeModal()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-warning">更新</button>
            </div>
        </form>
    `;
    showModal('编辑子分类', content);
}

async function updateSubcategory(event, subcategoryId) {
    event.preventDefault();

    const categoryId = parseInt(document.getElementById('edit-subcategory-category').value);
    const subcategoryTitle = document.getElementById('edit-subcategory-title').value.trim();

    // 检查是否选择了分类
    if (!categoryId) {
        showNotification('请选择所属分类', 'error');
        return;
    }

    // 检查标题是否为空
    if (!subcategoryTitle) {
        showNotification('子分类标题不能为空', 'error');
        return;
    }

    // 检查在同一主分类下是否存在相同标题的其他子分类（排除当前编辑的子分类）
    const existingSubcategory = currentData.subcategories.find(sub =>
        sub.subcategory_id !== subcategoryId &&
        sub.category_id === categoryId &&
        sub.subcategory_title.toLowerCase() === subcategoryTitle.toLowerCase()
    );

    if (existingSubcategory) {
        const categoryName = currentData.categories.find(cat => cat.category_id === categoryId)?.category_title || '该分类';
        showNotification(`在 "${categoryName}" 下已存在子分类 "${subcategoryTitle}"，请使用其他名称`, 'error');
        return;
    }

    const data = {
        category_id: categoryId,
        subcategory_title: subcategoryTitle,
        subcategory_icon: 'mdi-folder-outline',
        subcategory_color: 'text-gray-600',
        subcategory_order: parseInt(document.getElementById('edit-subcategory-order').value)
    };

    try {
        const response = await fetch(`/admin/api/subcategories/${subcategoryId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            await loadSubcategories();
            updateCategoriesTable(); // 更新分类树显示
            showNotification('子分类更新成功', 'success');
        } else {
            showNotification(result.message || '更新失败', 'error');
        }
    } catch (error) {
        console.error('更新子分类失败:', error);
        showNotification('更新子分类失败', 'error');
    }
}

async function deleteSubcategory(subcategoryId) {
    const subcategory = currentData.subcategories.find(sub => sub.subcategory_id === subcategoryId);
    if (!subcategory) {
        showNotification('子分类不存在', 'error');
        return;
    }

    // 检查是否有书签
    const bookmarks = currentData.bookmarks.filter(bookmark => bookmark.subcategory_id === subcategoryId);

    if (bookmarks.length > 0) {
        const bookmarkNames = bookmarks.slice(0, 5).map(bookmark => bookmark.bookmark_title).join('、');
        const moreText = bookmarks.length > 5 ? `等 ${bookmarks.length} 个书签` : '';

        showWarningModal(
            '无法删除子分类',
            `子分类 "${subcategory.subcategory_title}" 下还有 ${bookmarks.length} 个书签：<br><br><strong>${bookmarkNames}${moreText}</strong><br><br>请先删除所有书签后再删除子分类。`
        );
        return;
    }

    // 显示确认删除弹窗
    showConfirmModal(
        '删除子分类确认',
        `确定要删除子分类 "<strong>${subcategory.subcategory_title}</strong>" 吗？<br><br>此操作不可撤销。`,
        async () => {
            try {
                const response = await fetch(`/admin/api/subcategories/${subcategoryId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    // 重新加载数据
                    await Promise.all([loadSubcategories(), loadBookmarks()]);
                    updateCategoriesTable();
                    loadStats();
                    showNotification('子分类删除成功', 'success');
                } else {
                    showNotification(result.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除子分类失败:', error);
                showNotification('删除子分类失败', 'error');
            }
        }
    );
}

// 书签相关操作
// 显示批量添加书签模态框
function showBatchAddBookmarkModal() {
    const subcategoryOptions = currentData.subcategories.map(sub => {
        const categoryTitle = currentData.categories.find(cat => cat.category_id === sub.category_id)?.category_title || '未知分类';
        return `<option value="${sub.subcategory_id}">📂 ${categoryTitle} / 📄 ${sub.subcategory_title}</option>`;
    }).join('');

    const content = `
        <form onsubmit="batchAddBookmarks(event)">
            <div class="mb-6">
                <!-- 说明文档 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex items-start">
                        <i class="mdi mdi-information text-blue-500 text-lg mr-2 mt-0.5"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-800 mb-2">批量添加格式说明</h4>
                            <p class="text-sm text-blue-700 mb-2">请按照以下格式输入，每行一个书签：</p>
                            <p class="text-sm font-mono bg-white px-2 py-1 rounded border text-blue-800 mb-2">
                                网址标题-URL-概述
                            </p>
                            <div class="text-xs text-blue-600">
                                <p>• 使用英文减号 "-" 分隔三个部分</p>
                                <p>• 概述部分可以为空，但减号不能省略</p>
                                <p>• 示例：GitHub-https://github.com-代码托管平台</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分类选择 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">所属分类 *</label>
                    <select id="batch-subcategory" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500">
                        <option value="">请选择分类</option>
                        ${subcategoryOptions}
                    </select>
                </div>

                <!-- 批量输入区域 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">批量书签数据 *</label>
                    <textarea id="batch-bookmarks-text"
                              rows="10"
                              required
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"
                              placeholder="请输入书签数据，每行一个，格式：网址标题-URL-概述&#10;&#10;示例：&#10;GitHub-https://github.com-代码托管平台&#10;Google-https://google.com-搜索引擎&#10;百度-https://baidu.com-中文搜索引擎"></textarea>
                    <p class="text-xs text-gray-500 mt-1">支持粘贴多行数据，系统会自动解析</p>
                </div>

                <!-- 预览区域 -->
                <div class="mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <label class="block text-sm font-medium text-gray-700">数据预览</label>
                        <button type="button" onclick="previewBatchBookmarks()" class="btn btn-primary btn-sm">
                            <i class="mdi mdi-eye mr-1"></i>
                            预览解析结果
                        </button>
                    </div>
                    <div id="batch-preview" class="border border-gray-200 rounded-md p-3 bg-gray-50 min-h-[100px] text-sm">
                        <p class="text-gray-500 text-center">点击"预览解析结果"查看数据解析情况</p>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="handleModalClose()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-purple">批量添加</button>
            </div>
        </form>
    `;

    showModal('批量添加书签', content, 'max-w-3xl', { hasForm: true });

    // 自动聚焦到分类选择框
    setTimeout(() => {
        const subcategorySelect = document.getElementById('batch-subcategory');
        if (subcategorySelect) subcategorySelect.focus();
    }, 100);
}

// 预览批量书签解析结果
function previewBatchBookmarks() {
    const textInput = document.getElementById('batch-bookmarks-text');
    const previewDiv = document.getElementById('batch-preview');

    if (!textInput || !previewDiv) return;

    const text = textInput.value.trim();
    if (!text) {
        previewDiv.innerHTML = '<p class="text-gray-500 text-center">请先输入书签数据</p>';
        return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const results = [];
    const errors = [];

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return;

        const parts = trimmedLine.split('-');
        if (parts.length < 2) {
            errors.push(`第 ${index + 1} 行：格式错误，至少需要"标题-URL"`);
            return;
        }

        const title = parts[0].trim();
        const url = parts[1].trim();
        const description = parts.length > 2 ? parts.slice(2).join('-').trim() : '';

        if (!title) {
            errors.push(`第 ${index + 1} 行：标题不能为空`);
            return;
        }

        if (!url) {
            errors.push(`第 ${index + 1} 行：URL不能为空`);
            return;
        }

        // 验证URL格式
        try {
            new URL(url);
        } catch (e) {
            errors.push(`第 ${index + 1} 行：URL格式不正确`);
            return;
        }

        results.push({ title, url, description, lineNumber: index + 1 });
    });

    // 生成预览HTML
    let html = '';

    if (results.length > 0) {
        html += `<div class="mb-4">
            <h5 class="text-sm font-medium text-green-700 mb-2">
                <i class="mdi mdi-check-circle mr-1"></i>
                解析成功 (${results.length} 条)
            </h5>
            <div class="space-y-2">`;

        results.forEach(item => {
            html += `
                <div class="bg-white border border-green-200 rounded p-2">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">${escapeHtml(item.title)}</p>
                            <p class="text-sm text-blue-600 break-all">${escapeHtml(item.url)}</p>
                            ${item.description ? `<p class="text-sm text-gray-600">${escapeHtml(item.description)}</p>` : ''}
                        </div>
                        <span class="text-xs text-gray-400 ml-2">#${item.lineNumber}</span>
                    </div>
                </div>`;
        });

        html += '</div></div>';
    }

    if (errors.length > 0) {
        html += `<div class="mb-4">
            <h5 class="text-sm font-medium text-red-700 mb-2">
                <i class="mdi mdi-alert-circle mr-1"></i>
                解析错误 (${errors.length} 条)
            </h5>
            <div class="space-y-1">`;

        errors.forEach(error => {
            html += `<p class="text-sm text-red-600">${escapeHtml(error)}</p>`;
        });

        html += '</div></div>';
    }

    if (results.length === 0 && errors.length === 0) {
        html = '<p class="text-gray-500 text-center">没有找到有效的书签数据</p>';
    }

    previewDiv.innerHTML = html;
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 旧的行管理函数已移除，现在使用文本格式批量输入

// 批量添加书签
async function batchAddBookmarks(event) {
    event.preventDefault();

    const subcategoryId = parseInt(document.getElementById('batch-subcategory').value);
    const textInput = document.getElementById('batch-bookmarks-text').value.trim();

    // 验证分类选择
    if (!subcategoryId) {
        showNotification('请选择所属分类', 'error');
        return;
    }

    // 验证输入内容
    if (!textInput) {
        showNotification('请输入书签数据', 'error');
        return;
    }

    // 解析文本数据
    const lines = textInput.split('\n').filter(line => line.trim());
    const bookmarks = [];
    const errors = [];

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        if (!trimmedLine) return;

        const parts = trimmedLine.split('-');
        if (parts.length < 2) {
            errors.push(`第 ${index + 1} 行：格式错误，至少需要"标题-URL"`);
            return;
        }

        const title = parts[0].trim();
        const url = parts[1].trim();
        const description = parts.length > 2 ? parts.slice(2).join('-').trim() : '';

        if (!title) {
            errors.push(`第 ${index + 1} 行：标题不能为空`);
            return;
        }

        if (!url) {
            errors.push(`第 ${index + 1} 行：URL不能为空`);
            return;
        }

        // 验证URL格式
        try {
            new URL(url);
        } catch (e) {
            errors.push(`第 ${index + 1} 行：URL格式不正确`);
            return;
        }

        bookmarks.push({
            bookmark_title: title,
            bookmark_url: url,
            bookmark_description: description,
            bookmark_icon: 'mdi-web', // 固定使用默认图标
            subcategory_id: subcategoryId,
            bookmark_order: 0,
            lineNumber: index + 1
        });
    });

    // 检查解析结果
    if (errors.length > 0) {
        showNotification(`数据格式错误，请检查：\n${errors.slice(0, 3).join('\n')}${errors.length > 3 ? '\n...' : ''}`, 'error');
        return;
    }

    if (bookmarks.length === 0) {
        showNotification('没有找到有效的书签数据', 'error');
        return;
    }

    try {
        // 显示加载状态
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="mdi mdi-loading mdi-spin mr-2"></i>添加中...';
        submitBtn.disabled = true;

        let successCount = 0;
        let failCount = 0;
        const failedItems = [];

        // 逐个添加书签
        for (let i = 0; i < bookmarks.length; i++) {
            try {
                const response = await fetch('/admin/api/bookmarks', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(bookmarks[i])
                });

                const result = await response.json();

                if (result.success) {
                    successCount++;
                } else {
                    failCount++;
                    failedItems.push(`第 ${bookmarks[i].lineNumber} 行 "${bookmarks[i].bookmark_title}": ${result.message || '添加失败'}`);
                }
            } catch (error) {
                failCount++;
                failedItems.push(`第 ${bookmarks[i].lineNumber} 行 "${bookmarks[i].bookmark_title}": 网络错误`);
            }
        }

        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // 显示结果
        if (successCount > 0) {
            await loadBookmarks();
            updateCategoriesTable(); // 更新分类统计
            loadStats();
        }

        if (failCount === 0) {
            closeModal();
            showNotification(`成功添加 ${successCount} 个书签`, 'success');
        } else if (successCount > 0) {
            showNotification(`成功添加 ${successCount} 个书签，${failCount} 个失败`, 'warning');
            if (failedItems.length > 0) {
                console.error('批量添加失败项目:', failedItems);
            }
        } else {
            showNotification(`所有书签添加失败`, 'error');
            if (failedItems.length > 0) {
                console.error('批量添加失败项目:', failedItems);
            }
        }

    } catch (error) {
        console.error('批量添加书签失败:', error);
        showNotification('批量添加书签失败', 'error');
    }
}

function showAddBookmarkModal() {
    const subcategoryOptions = currentData.subcategories.map(sub =>
        `<option value="${sub.subcategory_id}">📂 ${sub.category_title} / 📄 ${sub.subcategory_title}</option>`
    ).join('');

    // 计算当前最大排序值
    const maxOrder = currentData.bookmarks.length > 0
        ? Math.max(...currentData.bookmarks.map(bookmark => bookmark.bookmark_order || 0))
        : 0;
    const nextOrder = maxOrder + 1;

    const content = `
        <form onsubmit="addBookmark(event)">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">所属子分类</label>
                <select id="bookmark-subcategory" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" onchange="updateBookmarkOrder()">
                    <option value="">请选择子分类</option>
                    ${subcategoryOptions}
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">书签标题</label>
                <input type="text" id="bookmark-title" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">网址</label>
                <input type="url" id="bookmark-url" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                <textarea id="bookmark-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    排序
                    <span class="text-xs text-gray-500" id="bookmark-order-hint">(当前建议值: ${nextOrder})</span>
                </label>
                <input type="number" id="bookmark-order" value="${nextOrder}" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">数字越小排序越靠前，可以修改为其他数值</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="handleModalClose()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-purple">添加</button>
            </div>
        </form>
    `;
    showModal('添加书签', content, 'w-96', { hasForm: true });

    // 自动聚焦到子分类选择框
    setTimeout(() => {
        const subcategorySelect = document.getElementById('bookmark-subcategory');
        if (subcategorySelect) subcategorySelect.focus();
    }, 100);
}

// 动态更新书签排序建议值
function updateBookmarkOrder() {
    const selectedSubcategoryId = parseInt(document.getElementById('bookmark-subcategory').value);
    if (!selectedSubcategoryId) return;

    // 计算该子分类下书签的最大排序值
    const subcategoryBookmarks = currentData.bookmarks.filter(bookmark => bookmark.subcategory_id === selectedSubcategoryId);
    const maxOrder = subcategoryBookmarks.length > 0
        ? Math.max(...subcategoryBookmarks.map(bookmark => bookmark.bookmark_order || 0))
        : 0;
    const nextOrder = maxOrder + 1;

    // 更新排序输入框和提示
    const orderInput = document.getElementById('bookmark-order');
    const orderHint = document.getElementById('bookmark-order-hint');

    if (orderInput && orderHint) {
        orderInput.value = nextOrder;
        orderHint.textContent = `(该子分类下建议值: ${nextOrder})`;
    }
}

async function addBookmark(event) {
    event.preventDefault();

    const data = {
        subcategory_id: parseInt(document.getElementById('bookmark-subcategory').value),
        bookmark_title: document.getElementById('bookmark-title').value,
        bookmark_url: document.getElementById('bookmark-url').value,
        bookmark_description: document.getElementById('bookmark-description').value,
        bookmark_order: parseInt(document.getElementById('bookmark-order').value)
    };

    try {
        const response = await fetch('/admin/api/bookmarks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            await loadBookmarks();
            updateCategoriesTable(); // 更新分类统计
            loadStats();
            showNotification('书签添加成功', 'success');
        } else {
            showNotification(result.message || '添加失败', 'error');
        }
    } catch (error) {
        console.error('添加书签失败:', error);
        showNotification('添加书签失败', 'error');
    }
}

function editBookmark(bookmarkId) {
    const bookmark = currentData.bookmarks.find(b => b.bookmark_id === bookmarkId);
    if (!bookmark) return;

    const subcategoryOptions = currentData.subcategories.map(sub =>
        `<option value="${sub.subcategory_id}" ${sub.subcategory_id === bookmark.subcategory_id ? 'selected' : ''}>📂 ${sub.category_title} / 📄 ${sub.subcategory_title}</option>`
    ).join('');

    const content = `
        <form onsubmit="updateBookmark(event, ${bookmarkId})">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">所属子分类</label>
                <select id="edit-bookmark-subcategory" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    ${subcategoryOptions}
                </select>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">书签标题</label>
                <input type="text" id="edit-bookmark-title" value="${bookmark.bookmark_title}" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">网址</label>
                <input type="url" id="edit-bookmark-url" value="${bookmark.bookmark_url}" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                <textarea id="edit-bookmark-description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">${bookmark.bookmark_description || ''}</textarea>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">排序</label>
                <input type="number" id="edit-bookmark-order" value="${bookmark.bookmark_order || 0}" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <p class="text-xs text-gray-500 mt-1">数字越小排序越靠前</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" onclick="handleModalClose()" class="btn btn-outline">取消</button>
                <button type="submit" class="btn btn-warning">更新</button>
            </div>
        </form>
    `;
    showModal('编辑书签', content, 'w-96', { hasForm: true });

    // 自动聚焦到标题输入框
    setTimeout(() => {
        const titleInput = document.getElementById('edit-bookmark-title');
        if (titleInput) {
            titleInput.focus();
            titleInput.select(); // 选中现有文本便于编辑
        }
    }, 100);
}

async function updateBookmark(event, bookmarkId) {
    event.preventDefault();

    const data = {
        subcategory_id: parseInt(document.getElementById('edit-bookmark-subcategory').value),
        bookmark_title: document.getElementById('edit-bookmark-title').value,
        bookmark_url: document.getElementById('edit-bookmark-url').value,
        bookmark_description: document.getElementById('edit-bookmark-description').value,
        bookmark_order: parseInt(document.getElementById('edit-bookmark-order').value)
    };

    try {
        const response = await fetch(`/admin/api/bookmarks/${bookmarkId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            closeModal();
            await loadBookmarks();
            updateCategoriesTable(); // 更新分类统计
            showNotification('书签更新成功', 'success');
        } else {
            showNotification(result.message || '更新失败', 'error');
        }
    } catch (error) {
        console.error('更新书签失败:', error);
        showNotification('更新书签失败', 'error');
    }
}

async function deleteBookmark(bookmarkId) {
    const bookmark = currentData.bookmarks.find(b => b.bookmark_id === bookmarkId);
    if (!bookmark) {
        showNotification('书签不存在', 'error');
        return;
    }

    // 显示确认删除弹窗
    showConfirmModal(
        '删除书签确认',
        `确定要删除书签 "<strong>${bookmark.bookmark_title}</strong>" 吗？<br><br><span class="text-xs text-gray-400">${bookmark.bookmark_url}</span><br><br>此操作不可撤销。`,
        async () => {
            try {
                const response = await fetch(`/admin/api/bookmarks/${bookmarkId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    // 重新加载数据
                    await loadBookmarks();
                    updateCategoriesTable(); // 更新书签统计
                    loadStats();
                    showNotification('书签删除成功', 'success');
                } else {
                    showNotification(result.message || '删除失败', 'error');
                }
            } catch (error) {
                console.error('删除书签失败:', error);
                showNotification('删除书签失败', 'error');
            }
        }
    );
}





// 检查用户认证状态
async function checkAuthStatus() {
    try {
        const sessionId = getSessionId();
        if (!sessionId) {
            return false;
        }

        const response = await fetch('/api/auth/verify', {
            headers: {
                'Authorization': `Bearer ${sessionId}`
            }
        });

        if (response.ok) {
            const result = await response.json();
            return result.success;
        }

        return false;
    } catch (error) {
        console.error('验证认证状态失败:', error);
        return false;
    }
}

// 获取会话ID
function getSessionId() {
    return localStorage.getItem('sessionId') || getCookie('sessionId');
}

// 获取Cookie
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

// 用户登出
async function logout() {
    try {
        const sessionId = getSessionId();
        if (sessionId) {
            // 调用登出API
            await fetch('/api/auth/logout', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${sessionId}`
                }
            });
        }
    } catch (error) {
        console.error('登出请求失败:', error);
    } finally {
        // 清除本地存储的会话信息
        localStorage.removeItem('sessionId');
        document.cookie = 'sessionId=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

        // 重定向到登录页面
        window.location.href = '/login';
    }
}

// ==================== 表单处理 ====================








