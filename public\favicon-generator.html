<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
</head>
<body>
    <h1>Favicon Generator</h1>
    <canvas id="canvas" width="32" height="32" style="border: 1px solid #ccc; image-rendering: pixelated; width: 128px; height: 128px;"></canvas>
    <br><br>
    <button onclick="generateFavicon()">Generate Favicon</button>
    <br><br>
    <div id="output"></div>

    <script>
        function generateFavicon() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, 32, 32);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, 32, 32);
            gradient.addColorStop(0, '#667eea');
            gradient.addColorStop(1, '#764ba2');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(16, 16, 15, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制书签形状
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.moveTo(8, 6);
            ctx.lineTo(24, 6);
            ctx.lineTo(24, 24);
            ctx.lineTo(20, 20);
            ctx.lineTo(16, 24);
            ctx.lineTo(12, 20);
            ctx.lineTo(8, 24);
            ctx.closePath();
            ctx.fill();
            
            // 绘制装饰线条
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(10, 10);
            ctx.lineTo(22, 10);
            ctx.moveTo(10, 13);
            ctx.lineTo(20, 13);
            ctx.moveTo(10, 16);
            ctx.lineTo(21, 16);
            ctx.stroke();
            
            // 绘制星标
            ctx.fillStyle = '#fbbf24';
            ctx.beginPath();
            const centerX = 16, centerY = 19, radius = 2;
            for (let i = 0; i < 5; i++) {
                const angle = (i * 144 - 90) * Math.PI / 180;
                const x = centerX + Math.cos(angle) * radius;
                const y = centerY + Math.sin(angle) * radius;
                if (i === 0) ctx.moveTo(x, y);
                else ctx.lineTo(x, y);
            }
            ctx.closePath();
            ctx.fill();
            
            // 转换为数据URL
            const dataURL = canvas.toDataURL('image/png');
            
            // 显示结果
            document.getElementById('output').innerHTML = `
                <h3>Generated Favicon:</h3>
                <img src="${dataURL}" style="width: 64px; height: 64px; image-rendering: pixelated;">
                <br><br>
                <h3>Data URL (copy this):</h3>
                <textarea style="width: 100%; height: 100px;">${dataURL}</textarea>
                <br><br>
                <a href="${dataURL}" download="favicon.png">Download PNG</a>
            `;
        }
        
        // 自动生成
        generateFavicon();
    </script>
</body>
</html>
