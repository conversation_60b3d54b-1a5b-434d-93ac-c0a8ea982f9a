<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="bookmarkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="starGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fbbf24;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="32" cy="32" r="30" fill="url(#bookmarkGradient)" filter="url(#shadow)"/>
  
  <!-- 主书签形状 -->
  <path d="M20 12 L44 12 Q46 12 46 14 L46 48 Q46 50 44 50 L40 46 L32 50 L24 46 L20 50 Q18 50 18 48 L18 14 Q18 12 20 12 Z" 
        fill="white" 
        opacity="0.95" 
        stroke="rgba(255,255,255,0.3)" 
        stroke-width="1"/>
  
  <!-- 书签内的装饰线条 -->
  <line x1="24" y1="20" x2="40" y2="20" stroke="url(#bookmarkGradient)" stroke-width="2" stroke-linecap="round"/>
  <line x1="24" y1="26" x2="36" y2="26" stroke="url(#bookmarkGradient)" stroke-width="2" stroke-linecap="round"/>
  <line x1="24" y1="32" x2="38" y2="32" stroke="url(#bookmarkGradient)" stroke-width="2" stroke-linecap="round"/>
  
  <!-- 精选星标 -->
  <path d="M32 36 L34 40 L38 40 L35 43 L36 47 L32 45 L28 47 L29 43 L26 40 L30 40 Z" 
        fill="url(#starGradient)" 
        stroke="white" 
        stroke-width="1"/>
  
  <!-- 高光效果 -->
  <ellipse cx="26" cy="18" rx="4" ry="2" fill="rgba(255,255,255,0.4)" transform="rotate(-20 26 18)"/>
</svg>
