<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔖 精选导航 - 您的专属书签导航</title>
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* CSS变量定义 - 浅色主题 */
        :root {
            --bg-primary: #f8fafc;
            --bg-secondary: rgba(255, 255, 255, 0.95);
            --bg-tertiary: rgba(255, 255, 255, 0.98);
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-tertiary: #9ca3af;
            --border-primary: rgba(229, 231, 235, 0.8);
            --border-secondary: rgba(209, 213, 219, 0.8);
            --shadow-light: rgba(0, 0, 0, 0.1);
            --shadow-medium: rgba(0, 0, 0, 0.15);
            --glass-bg: rgba(255, 255, 255, 0.95);
            --glass-border: rgba(229, 231, 235, 0.8);
            --card-bg: rgba(255, 255, 255, 0.95);
            --card-hover-bg: rgba(255, 255, 255, 0.98);
            --nav-bg: rgba(255, 255, 255, 0.98);
            --sidebar-bg: rgba(255, 255, 255, 0.98);
            --search-bg: rgba(255, 255, 255, 0.95);
            --search-focus-bg: rgba(255, 255, 255, 0.98);
            --hover-bg: rgba(243, 244, 246, 0.6);
            --active-bg: rgba(243, 244, 246, 0.8);
            --scrollbar-track: rgba(243, 244, 246, 0.3);
            --scrollbar-thumb: rgba(156, 163, 175, 0.6);
            --scrollbar-thumb-hover: rgba(107, 114, 128, 0.8);
        }

        /* 深色主题 */
        [data-theme="dark"] {
            --bg-primary: #0f172a;
            --bg-secondary: rgba(30, 41, 59, 0.95);
            --bg-tertiary: rgba(30, 41, 59, 0.98);
            --text-primary: #ffffff;
            --text-secondary: #e2e8f0;
            --text-tertiary: #cbd5e1;
            --border-primary: rgba(51, 65, 85, 0.8);
            --border-secondary: rgba(71, 85, 105, 0.8);
            --shadow-light: rgba(0, 0, 0, 0.3);
            --shadow-medium: rgba(0, 0, 0, 0.4);
            --glass-bg: rgba(30, 41, 59, 0.95);
            --glass-border: rgba(51, 65, 85, 0.8);
            --card-bg: rgba(30, 41, 59, 0.95);
            --card-hover-bg: rgba(51, 65, 85, 0.98);
            --nav-bg: rgba(15, 23, 42, 0.98);
            --sidebar-bg: rgba(15, 23, 42, 0.98);
            --search-bg: rgba(30, 41, 59, 0.95);
            --search-focus-bg: rgba(51, 65, 85, 0.98);
            --hover-bg: rgba(51, 65, 85, 0.6);
            --active-bg: rgba(51, 65, 85, 0.8);
            --scrollbar-track: rgba(51, 65, 85, 0.3);
            --scrollbar-thumb: rgba(100, 116, 139, 0.6);
            --scrollbar-thumb-hover: rgba(148, 163, 184, 0.8);
        }

        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .glass-effect {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            box-shadow: 0 4px 6px -1px var(--shadow-light);
        }

        .category-section:target {
            scroll-margin-top: 90px;
        }

        .sidebar {
            height: calc(100vh - 90px);
            overflow-y: auto;
            top: 90px;
            background: var(--sidebar-bg);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--border-primary);
            box-shadow: 2px 0 4px var(--shadow-light);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        .main-content {
            height: calc(100vh - 90px);
            overflow-y: auto;
            padding-top: 90px;
            background: transparent;
        }

        html {
            scroll-behavior: smooth;
            scroll-padding-top: 90px;
        }

        /* 现代化卡片设计 - 5列水平布局 */
        .bookmark-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-primary);
            border-radius: 12px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px -1px var(--shadow-light);
            height: 72px; /* 适合5列布局的紧凑高度 */
        }

        .bookmark-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px -3px var(--shadow-medium), 0 4px 6px -2px var(--shadow-light);
            background: var(--card-hover-bg);
            border-color: rgba(99, 102, 241, 0.3);
        }

        /* 卡片内容区域 */
        .bookmark-content {
            height: 100%;
            display: flex;
            align-items: center;
        }

        /* 图标容器样式 - 5列布局优化 */
        .bookmark-card .icon-container {
            transition: all 0.3s ease;
            width: 40px;
            height: 40px;
        }

        .bookmark-card:hover .icon-container {
            transform: scale(1.05);
        }

        /* 5列布局的图标尺寸调整 */
        .bookmark-card .icon-container .mdi {
            font-size: 20px;
        }

        .bookmark-card .icon-container div {
            width: 32px;
            height: 32px;
            font-size: 14px;
        }

        .bookmark-card .icon-container img {
            width: 28px;
            height: 28px;
        }

        /* 响应式调整 */
        @media (min-width: 1536px) {
            .bookmark-card {
                height: 72px;
            }
        }

        @media (max-width: 1535px) {
            .bookmark-card {
                height: 76px;
            }
            .bookmark-card .icon-container {
                width: 44px;
                height: 44px;
            }
        }

        @media (max-width: 1280px) {
            .bookmark-card {
                height: 80px;
            }
            .bookmark-card .icon-container {
                width: 48px;
                height: 48px;
            }
        }

        @media (max-width: 1024px) {
            .bookmark-card {
                height: 76px;
            }
        }

        @media (max-width: 768px) {
            .bookmark-card {
                height: 72px;
            }
        }

        .bookmark-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 10;
        }

        .bookmark-card:hover::before {
            opacity: 1;
        }

        /* 文本截断样式 */
        .line-clamp-1 {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.3;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
        }

        /* 卡片内容区域优化 */
        .bookmark-content {
            min-height: 55px;
            max-height: 70px;
        }

        /* 卡片整体高度控制 */
        .bookmark-card {
            height: auto;
            max-height: 220px;
        }

        /* 分割线样式 */
        .bookmark-card .border-t {
            position: relative;
        }

        .bookmark-card .border-t::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(229, 231, 235, 0.8), transparent);
        }

        /* 移除快照相关样式，现在使用图标+文字布局 */



        /* 搜索框美化 */
        #searchInput {
            background: var(--search-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-secondary);
            transition: all 0.3s ease;
            color: var(--text-primary);
        }

        #searchInput:focus {
            background: var(--search-focus-bg);
            border-color: var(--text-tertiary);
            box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.1);
            outline: none;
        }

        #searchInput::placeholder {
            color: var(--text-tertiary);
        }

        /* 导航栏美化 */
        .navbar {
            background: var(--nav-bg);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-primary);
            box-shadow: 0 1px 3px var(--shadow-light);
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        /* 分类标签美化 */
        .category-nav-item {
            border-radius: 12px;
            transition: all 0.3s ease;
            margin-bottom: 4px;
            color: var(--text-secondary);
        }

        .category-nav-item:hover {
            background: var(--hover-bg);
            transform: translateX(4px);
            color: var(--text-primary);
        }

        .category-nav-item.active {
            color: var(--text-primary);
            border-left: 3px solid #6366f1;
        }

        /* 子分类标签美化 */
        .subcategory-tab {
            background: transparent;
            border: none;
            border-radius: 6px;
            transition: all 0.2s ease;
            position: relative;
            padding: 8px 16px;
            color: var(--text-secondary);
        }

        .subcategory-tab:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
        }

        .subcategory-tab.active {
            color: var(--text-primary);
            font-weight: 500;
        }

        .subcategory-tab.active span {
            position: relative;
        }

        .subcategory-tab.active span::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #6366f1;
            border-radius: 1px;
            animation: slideIn 0.3s ease-out;
            transform-origin: left center;
        }

        @keyframes slideIn {
            from {
                transform: scaleX(0);
                opacity: 0;
            }
            to {
                transform: scaleX(1);
                opacity: 1;
            }
        }

        /* 加载动画美化 */
        .loading-spinner {
            background: #6366f1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 添加动画类 */
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* 标题样式 */
        .gradient-text {
            color: #374151;
            font-weight: 700;
        }

        /* 左侧子分类激活样式 */
        .nav-subcategory-link.active {
            color: var(--text-primary);
            font-weight: 500;
            position: relative;
        }

        .nav-subcategory-link.active span {
            position: relative;
        }

        .nav-subcategory-link.active span::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: #6366f1;
            border-radius: 1px;
            animation: slideIn 0.3s ease-out;
            transform-origin: left center;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }

        /* 滚动条美化 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--scrollbar-track);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--scrollbar-thumb);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--scrollbar-thumb-hover);
        }

        /* 主题切换按钮样式 */
        .theme-toggle {
            background: var(--bg-secondary);
            border: 1px solid var(--border-primary);
            color: var(--text-secondary);
            transition: all 0.3s ease;
        }

        .theme-toggle:hover {
            background: var(--hover-bg);
            color: var(--text-primary);
            transform: scale(1.05);
        }

        /* 图标颜色适配 */
        .text-icon {
            color: var(--text-secondary);
        }

        .text-icon:hover {
            color: var(--text-primary);
        }

        /* 标题文字适配 */
        .gradient-text {
            color: var(--text-primary);
            font-weight: 700;
        }

        /* 描述文字适配 */
        .text-description {
            color: var(--text-tertiary);
        }

        /* 加载文字适配 */
        .loading-text {
            color: var(--text-primary);
        }

        .loading-subtext {
            color: var(--text-secondary);
        }

        /* 卡片文字颜色适配 */
        .bookmark-title {
            color: var(--text-primary);
        }

        .bookmark-description {
            color: var(--text-secondary);
        }

        /* 深色主题下的卡片文字优化 */
        [data-theme="dark"] .bookmark-title {
            color: #ffffff; /* 纯白色标题 */
        }

        [data-theme="dark"] .bookmark-description {
            color: #e2e8f0; /* 浅灰白色描述 */
        }

        /* 深色主题下的左侧分类文字优化 */
        [data-theme="dark"] .category-nav-item {
            color: #ffffff; /* 默认白色字体 */
        }

        [data-theme="dark"] .category-nav-item:hover {
            color: #ffffff; /* 悬浮时保持白色 */
        }

        [data-theme="dark"] .category-nav-item.active {
            color: #ffffff; /* 激活时保持白色 */
        }

        /* 深色主题下的子分类文字优化 */
        [data-theme="dark"] .nav-subcategory-link {
            color: #e2e8f0; /* 子分类默认浅灰白 */
        }

        [data-theme="dark"] .nav-subcategory-link:hover {
            color: #ffffff; /* 悬浮时变白色 */
        }

        [data-theme="dark"] .nav-subcategory-link.active {
            color: #ffffff; /* 激活时白色 */
        }

        /* 深色主题下的子分类标签优化 */
        [data-theme="dark"] .subcategory-tab {
            color: #e2e8f0; /* 默认浅灰白 */
        }

        [data-theme="dark"] .subcategory-tab:hover {
            color: #ffffff; /* 悬浮时白色 */
        }

        [data-theme="dark"] .subcategory-tab.active {
            color: #ffffff; /* 激活时白色 */
        }

        /* 右侧主分类标题样式 */
        .category-main-title {
            color: var(--text-primary);
        }

        .category-subtitle {
            color: var(--text-secondary);
        }

        /* 深色主题下的右侧分类标题优化 */
        [data-theme="dark"] .category-main-title {
            color: #ffffff; /* 主标题白色 */
        }

        [data-theme="dark"] .category-subtitle {
            color: #e2e8f0; /* 副标题浅灰白 */
        }

        /* 页尾样式 */
        .footer-section {
            border-top: 1px solid var(--border-primary);
            margin-top: 4rem;
            padding-top: 2rem;
        }

        .footer-link {
            color: var(--text-secondary);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-link:hover {
            color: var(--text-primary);
            text-decoration: underline;
        }

        .footer-text {
            color: var(--text-secondary);
        }

        .footer-copyright {
            color: var(--text-tertiary);
        }

        /* 深色主题下的页尾优化 */
        [data-theme="dark"] .footer-section {
            border-top-color: var(--border-primary);
        }

        [data-theme="dark"] .footer-link {
            color: #e2e8f0;
        }

        [data-theme="dark"] .footer-link:hover {
            color: #ffffff;
        }

        [data-theme="dark"] .footer-text {
            color: #e2e8f0;
        }

        [data-theme="dark"] .footer-copyright {
            color: #cbd5e1;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="fixed top-0 left-0 right-0 navbar z-50">
        <nav class="container mx-auto px-6 h-20 flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <i class="mdi mdi-bookmark text-white text-xl"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold gradient-text">精选导航</h1>
                    <p class="text-xs text-description">您的专属书签导航</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="relative">
                    <input type="text"
                           id="searchInput"
                           class="w-72 px-5 py-3 pr-20 rounded-2xl focus:outline-none"
                           placeholder="🔍 搜索您的书签..."
                           onkeyup="handleSearch(event)"
                           oninput="toggleClearButton(); handleSearch(event)">
                    <i class="mdi mdi-magnify absolute right-12 top-1/2 -translate-y-1/2 text-icon text-xl"></i>
                    <button id="clearSearchBtn"
                            class="absolute right-4 top-1/2 -translate-y-1/2 text-icon hover:text-primary transition-colors hidden"
                            onclick="clearSearch()">
                        <i class="mdi mdi-close-circle text-xl"></i>
                    </button>
                </div>
                <button id="themeToggle"
                        class="theme-toggle p-3 rounded-xl transition-all duration-300"
                        onclick="toggleTheme()"
                        title="切换主题">
                    <i id="themeIcon" class="mdi mdi-weather-night text-xl"></i>
                </button>
                <button class="theme-toggle p-3 rounded-xl transition-all duration-300"
                        onclick="goToAdmin()"
                        title="后台管理">
                    <i class="mdi mdi-shield-account text-xl"></i>
                </button>
                <button class="md:hidden theme-toggle p-3 rounded-xl transition-all duration-300" onclick="toggleSidebar()">
                    <i class="mdi mdi-menu text-xl"></i>
                </button>
            </div>
        </nav>
    </header>

    <div class="flex pt-20">
        <!-- 左侧分类导航 -->
        <aside class="sidebar w-60 fixed left-0 top-20 p-6" id="sidebar">
            <nav id="category-nav" class="space-y-2">
                <!-- 分类导航将通过JavaScript动态加载 -->
            </nav>
        </aside>

        <!-- 右侧主内容区 -->
        <main class="main-content flex-1 ml-60 p-8">
            <div id="categories" class="space-y-16">
                <div id="loading" class="text-center py-16">
                    <div class="loading-spinner w-12 h-12 mx-auto mb-4"></div>
                    <p class="loading-text text-lg font-medium">✨ 正在加载精选内容...</p>
                    <p class="loading-subtext text-sm mt-2">为您准备最优质的导航体验</p>
                </div>
            </div>

            <!-- 页尾备案信息 -->
            <footer class="footer-section mt-16 pt-8 border-t border-gray-200 dark:border-gray-700">
                <div class="text-center space-y-4">
                    <!-- 备案信息 -->
                    <div class="flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-6">
                        <!-- ICP备案 -->
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 rounded bg-blue-600 flex items-center justify-center">
                                <i class="mdi mdi-shield-check text-white text-sm"></i>
                            </div>
                            <a href="#" onclick="return false;" class="footer-link text-sm">
                                京ICP备12345678号-1
                            </a>
                        </div>

                        <!-- 公安备案 -->
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 rounded bg-green-600 flex items-center justify-center">
                                <i class="mdi mdi-police-badge text-white text-sm"></i>
                            </div>
                            <a href="#" onclick="return false;" class="footer-link text-sm">
                                京公网安备 11010802012345号
                            </a>
                        </div>

                        <!-- 网站认证 -->
                        <div class="flex items-center space-x-2">
                            <div class="w-6 h-6 rounded bg-orange-600 flex items-center justify-center">
                                <i class="mdi mdi-certificate text-white text-sm"></i>
                            </div>
                            <span class="footer-text text-sm">网站安全认证</span>
                        </div>
                    </div>

                    <!-- 版权信息 -->
                    <div class="footer-copyright text-xs opacity-75">
                        <p>© 2024 精选导航. All rights reserved.</p>
                        <p class="mt-1">本站致力于为用户提供优质的网站导航服务</p>
                    </div>
                </div>
            </footer>
        </main>
    </div>

    <script>
        // 主题切换功能
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            const themeIcon = document.getElementById('themeIcon');

            if (savedTheme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
                themeIcon.className = 'mdi mdi-weather-sunny text-xl';
            } else {
                document.documentElement.removeAttribute('data-theme');
                themeIcon.className = 'mdi mdi-weather-night text-xl';
            }
        }

        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const themeIcon = document.getElementById('themeIcon');

            if (currentTheme === 'dark') {
                // 切换到浅色主题
                document.documentElement.removeAttribute('data-theme');
                themeIcon.className = 'mdi mdi-weather-night text-xl';
                localStorage.setItem('theme', 'light');
            } else {
                // 切换到深色主题
                document.documentElement.setAttribute('data-theme', 'dark');
                themeIcon.className = 'mdi mdi-weather-sunny text-xl';
                localStorage.setItem('theme', 'dark');
            }
        }

        // 页面加载时初始化主题
        document.addEventListener('DOMContentLoaded', function() {
            initTheme();
        });

        // 图标颜色生成函数
        function generateLetterColor(letter) {
            const colors = [
                'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
                'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
                'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                'linear-gradient(135deg, #ff8a80 0%, #ea6100 100%)',
                'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)',
                'linear-gradient(135deg, #a6c1ee 0%, #fbc2eb 100%)',
                'linear-gradient(135deg, #fdcbf1 0%, #fdcbf1 100%)',
                'linear-gradient(135deg, #e0c3fc 0%, #9bb5ff 100%)',
                'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
                'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
                'linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%)',
                'linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)',
                'linear-gradient(135deg, #fd63a3 0%, #fc3a52 100%)',
                'linear-gradient(135deg, #fc466b 0%, #3f5efb 100%)',
                'linear-gradient(135deg, #3742fa 0%, #2f3542 100%)',
                'linear-gradient(135deg, #f8b500 0%, #fceabb 100%)',
                'linear-gradient(135deg, #70a1ff 0%, #5352ed 100%)',
                'linear-gradient(135deg, #7bed9f 0%, #70a1ff 100%)',
                'linear-gradient(135deg, #ff6b6b 0%, #ffa726 100%)',
                'linear-gradient(135deg, #4834d4 0%, #686de0 100%)',
                'linear-gradient(135deg, #30336b 0%, #535c68 100%)'
            ];

            const index = letter.charCodeAt(0) % colors.length;
            return colors[index];
        }

        function generateIconColor(letter) {
            const colors = [
                '#667eea', '#f093fb', '#4facfe', '#43e97b', '#fa709a',
                '#a8edea', '#ff9a9e', '#ffecd2', '#ff8a80', '#84fab0',
                '#a6c1ee', '#fdcbf1', '#e0c3fc', '#ffeaa7', '#74b9ff',
                '#fd79a8', '#6c5ce7', '#fd63a3', '#fc466b', '#3742fa',
                '#f8b500', '#70a1ff', '#7bed9f', '#ff6b6b', '#4834d4'
            ];

            const index = letter.charCodeAt(0) % colors.length;
            return colors[index];
        }

        // Favicon缓存管理 - 优化版
        const faviconCache = {
            storage: new Map(),

            get(domain) {
                const cached = this.storage.get(domain);
                if (cached) {
                    // 检查是否过期（30天）
                    const now = Date.now();
                    const expiry = 30 * 24 * 60 * 60 * 1000; // 30天
                    if (now - cached.timestamp > expiry) {
                        this.storage.delete(domain);
                        this.save();
                        return null;
                    }
                }
                return cached;
            },

            set(domain, success, url = null, size = 64) {
                const cacheData = {
                    success,
                    url,
                    size,
                    timestamp: Date.now()
                };
                this.storage.set(domain, cacheData);
                this.save();

                console.log(`💾 缓存favicon: ${domain} - ${success ? '成功' : '失败'}`);
            },

            save() {
                try {
                    const cacheData = Object.fromEntries(this.storage);
                    localStorage.setItem('faviconCache_v2', JSON.stringify(cacheData));
                } catch (e) {
                    console.warn('无法保存favicon缓存到localStorage:', e);
                    // 如果存储空间不足，清理一些旧缓存
                    this.cleanup();
                }
            },

            load() {
                try {
                    // 尝试加载新版本缓存
                    let cached = localStorage.getItem('faviconCache_v2');
                    if (!cached) {
                        // 如果没有新版本，尝试迁移旧版本
                        cached = localStorage.getItem('faviconCache');
                        if (cached) {
                            localStorage.removeItem('faviconCache'); // 清理旧版本
                        }
                    }

                    if (cached) {
                        const cacheData = JSON.parse(cached);
                        this.storage = new Map(Object.entries(cacheData));

                        // 清理过期缓存
                        this.cleanup();
                        console.log(`📋 加载favicon缓存: ${this.storage.size} 条记录`);
                    }
                } catch (e) {
                    console.warn('无法加载favicon缓存:', e);
                    this.storage = new Map();
                }
            },

            cleanup() {
                const now = Date.now();
                const expiry = 30 * 24 * 60 * 60 * 1000; // 30天
                let cleaned = 0;

                for (const [domain, data] of this.storage) {
                    if (now - data.timestamp > expiry) {
                        this.storage.delete(domain);
                        cleaned++;
                    }
                }

                if (cleaned > 0) {
                    console.log(`🧹 清理过期favicon缓存: ${cleaned} 条`);
                    this.save();
                }
            },

            clear() {
                this.storage.clear();
                localStorage.removeItem('faviconCache_v2');
                localStorage.removeItem('faviconCache'); // 同时清理旧版本
                console.log('🗑️ 清空favicon缓存');
            },

            getStats() {
                let success = 0, failed = 0;
                for (const [domain, data] of this.storage) {
                    if (data.success) success++;
                    else failed++;
                }
                return {
                    total: this.storage.size,
                    success,
                    failed,
                    successRate: this.storage.size > 0 ? (success / this.storage.size * 100).toFixed(1) + '%' : '0%'
                };
            }
        };

        // 初始化favicon缓存
        faviconCache.load();

        // Favicon加载成功处理
        function handleFaviconLoad(bookmarkId, domain) {
            const faviconImg = document.getElementById(`favicon-${bookmarkId}`);
            const container = document.getElementById(`icon-container-${bookmarkId}`);
            const letterIcon = document.getElementById(`letter-icon-${bookmarkId}`);
            const mdiIcon = document.getElementById(`mdi-icon-${bookmarkId}`);

            if (faviconImg && faviconImg.naturalWidth > 0 && faviconImg.naturalHeight > 0) {
                // 隐藏所有其他图标，显示favicon（最高优先级）
                if (letterIcon) letterIcon.classList.add('hidden');
                if (mdiIcon) mdiIcon.classList.add('hidden');

                faviconImg.classList.remove('hidden');
                faviconImg.classList.add('block');

                // 更新容器样式为白色背景，适合favicon显示
                container.style.background = 'rgba(255, 255, 255, 0.95)';
                container.style.borderColor = 'rgba(229, 231, 235, 0.8)';
                container.classList.add('group-hover:border-blue-200');

                // 缓存成功的favicon
                faviconCache.set(domain, true, faviconImg.src, 64);

                console.log(`✅ Favicon加载成功: ${domain} (书签ID: ${bookmarkId})`);
            } else {
                handleFaviconError(bookmarkId, domain);
            }
        }

        // Favicon加载失败处理
        function handleFaviconError(bookmarkId, domain) {
            const faviconImg = document.getElementById(`favicon-${bookmarkId}`);
            const letterIcon = document.getElementById(`letter-icon-${bookmarkId}`);
            const mdiIcon = document.getElementById(`mdi-icon-${bookmarkId}`);

            // 隐藏favicon
            if (faviconImg) {
                faviconImg.classList.add('hidden');
                faviconImg.classList.remove('block');
            }

            // 显示备用图标（MDI图标优先，然后是字母图标）
            if (mdiIcon) {
                mdiIcon.classList.remove('hidden');
                if (letterIcon) letterIcon.classList.add('hidden');
                console.log(`🔄 Favicon失败，显示MDI图标: ${domain} (书签ID: ${bookmarkId})`);
            } else if (letterIcon) {
                letterIcon.classList.remove('hidden');
                console.log(`🔄 Favicon失败，显示字母图标: ${domain} (书签ID: ${bookmarkId})`);
            }

            // 缓存失败状态
            faviconCache.set(domain, false);

            console.log(`❌ Favicon加载失败: ${domain} (书签ID: ${bookmarkId})`);
        }

        // 异步加载favicon - 优化版
        // 图标显示优先级：
        // 1. 初始显示：MDI图标（如果有）> 彩色字母（同级备用）
        // 2. 异步加载：谷歌Favicon（最高优先级）
        // 3. 失败降级：MDI图标 > 彩色字母
        function loadFavicon(bookmarkId, domain) {
            const cached = faviconCache.get(domain);

            // 检查缓存
            if (cached) {
                if (cached.success && cached.url) {
                    const faviconImg = document.getElementById(`favicon-${bookmarkId}`);
                    if (faviconImg) {
                        faviconImg.src = cached.url;
                        // 直接显示缓存的favicon
                        setTimeout(() => handleFaviconLoad(bookmarkId, domain), 50);
                    }
                    console.log(`📋 使用缓存的favicon: ${domain}`);
                } else {
                    // 缓存显示失败，使用备用图标
                    handleFaviconError(bookmarkId, domain);
                    console.log(`📋 使用缓存的失败状态，显示备用图标: ${domain}`);
                }
                return;
            }

            // 异步加载favicon
            const faviconImg = document.getElementById(`favicon-${bookmarkId}`);
            if (!faviconImg) return;

            // 构建64px的谷歌favicon URL
            const faviconUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;

            console.log(`🔄 开始加载favicon: ${domain}`);

            // 设置超时（8秒）
            const timeout = setTimeout(() => {
                console.log(`⏰ Favicon加载超时: ${domain}`);
                faviconCache.set(domain, false);
                handleFaviconError(bookmarkId, domain);
            }, 8000);

            // 清除之前的事件监听器
            faviconImg.onload = null;
            faviconImg.onerror = null;

            faviconImg.onload = () => {
                clearTimeout(timeout);

                // 验证图片是否有效
                if (faviconImg.naturalWidth > 0 && faviconImg.naturalHeight > 0) {
                    // 验证图片不是默认的空白图标
                    if (faviconImg.naturalWidth >= 16 && faviconImg.naturalHeight >= 16) {
                        console.log(`✅ Favicon加载成功: ${domain} (${faviconImg.naturalWidth}x${faviconImg.naturalHeight})`);
                        handleFaviconLoad(bookmarkId, domain);
                    } else {
                        console.log(`❌ Favicon尺寸过小: ${domain} (${faviconImg.naturalWidth}x${faviconImg.naturalHeight})`);
                        faviconCache.set(domain, false);
                        handleFaviconError(bookmarkId, domain);
                    }
                } else {
                    console.log(`❌ Favicon无效: ${domain}`);
                    faviconCache.set(domain, false);
                    handleFaviconError(bookmarkId, domain);
                }
            };

            faviconImg.onerror = () => {
                clearTimeout(timeout);
                console.log(`❌ Favicon加载失败: ${domain}`);
                faviconCache.set(domain, false);
                handleFaviconError(bookmarkId, domain);
            };

            // 开始加载
            faviconImg.src = faviconUrl;
        }

        // 在 loadCategories 函数前添加测试数据
        const mockData = {
            success: true,
            data: [
                {
                    category_id: 1,
                    category_title: "常用工具",
                    category_icon: "mdi-tools",
                    category_color: "text-blue-600",
                    subcategories: [
                        {
                            subcategory_id: 1,
                            subcategory_title: "开发工具",
                            subcategory_icon: "mdi-code-tags",
                            subcategory_color: "text-green-600",
                            links: [
                                {
                                    bookmark_id: 1,
                                    bookmark_title: "GitHub",
                                    bookmark_url: "https://github.com",
                                    bookmark_description: "全球最大的代码托管平台"
                                },
                                {
                                    bookmark_id: 2,
                                    bookmark_title: "Stack Overflow",
                                    bookmark_url: "https://stackoverflow.com",
                                    bookmark_description: "程序员问答社区"
                                }
                            ]
                        }
                    ]
                }
            ]
        };

        // 移除重复的favicon加载函数，使用上面定义的统一函数

        // 渲染子分类和书签的函数 - 4列布局
        function renderSubcategories(subcategories) {
            return subcategories.map((subcategory, index) => `
                <div id="subcategory-${subcategory.subcategory_id}"
                     class="subcategory"
                     style="${index === 0 ? '' : 'display: none;'}">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4">
                        ${subcategory.links.map(bookmark => {
                            const url = new URL(bookmark.bookmark_url);
                            const domain = url.hostname;
                            const firstLetter = bookmark.bookmark_title.charAt(0).toUpperCase();
                            const bookmarkId = bookmark.bookmark_id;

                            return `
                                <a href="${bookmark.bookmark_url}"
                                   target="_blank"
                                   onclick="incrementClicks(${bookmark.bookmark_id})"
                                   class="bookmark-card block group"
                                   title="${bookmark.bookmark_description || bookmark.bookmark_title}">
                                    <!-- 卡片内容 -->
                                    <div class="bookmark-content p-3 flex items-center space-x-2">
                                        <!-- 左侧图标 -->
                                        <div class="flex-shrink-0">
                                            <div class="icon-container rounded-xl flex items-center justify-center border transition-all duration-300"
                                                 id="icon-container-${bookmarkId}">
                                                <!-- MDI图标（如果有的话） -->
                                                ${bookmark.bookmark_icon && bookmark.bookmark_icon.startsWith('mdi-') ? `
                                                    <i class="mdi ${bookmark.bookmark_icon} transition-colors"
                                                       style="color: ${generateIconColor(firstLetter)}"
                                                       id="mdi-icon-${bookmarkId}"></i>
                                                ` : ''}

                                                <!-- 彩色字母图标（备用显示） -->
                                                <div class="rounded-lg flex items-center justify-center text-white font-bold ${bookmark.bookmark_icon && bookmark.bookmark_icon.startsWith('mdi-') ? 'hidden' : ''}"
                                                     style="background: ${generateLetterColor(firstLetter)}"
                                                     id="letter-icon-${bookmarkId}">
                                                    ${firstLetter}
                                                </div>

                                                <!-- 异步加载的favicon，初始隐藏 -->
                                                <img src=""
                                                     class="rounded-lg hidden"
                                                     alt="${bookmark.bookmark_title} icon"
                                                     id="favicon-${bookmarkId}">
                                            </div>
                                        </div>

                                        <!-- 右侧内容 -->
                                        <div class="flex-1 min-w-0">
                                            <h3 class="text-xs font-semibold mb-0.5 line-clamp-1 bookmark-title">
                                                ${bookmark.bookmark_title}
                                            </h3>
                                            <p class="text-xs line-clamp-1 leading-tight bookmark-description">
                                                ${bookmark.bookmark_description || '精选网站推荐'}
                                            </p>
                                        </div>
                                    </div>
                                </a>
                            `;
                        }).join('')}
                    </div>
                </div>
            `).join('');
        }

        // 修改滚动监听函数
        function setupScrollSpy() {
            const sections = document.querySelectorAll('.category-section');
            const navItems = document.querySelectorAll('.category-nav-item');
            const headerHeight = 80; // 增加头部偏移量
            
            let ticking = false;
            window.addEventListener('scroll', () => {
                if (!ticking) {
                    window.requestAnimationFrame(() => {
                        const scrollPos = window.scrollY + headerHeight;
                        let currentSection = null;
                        
                        // 找到当前视窗中最靠上的部分
                        sections.forEach((section) => {
                            const sectionTop = section.offsetTop;
                            const sectionBottom = sectionTop + section.offsetHeight;
                            
                            if (scrollPos >= sectionTop && scrollPos < sectionBottom) {
                                currentSection = section;
                            }
                        });
                        
                        if (currentSection) {
                            const categoryId = currentSection.id.split('-')[1];
                            
                            // 更新所有导航项的状态
                            navItems.forEach(item => {
                                const itemCategoryId = item.querySelector('.category-nav-item')
                                    ?.getAttribute('data-category-id');
                                
                                if (itemCategoryId === categoryId) {
                                    item.classList.add('active');
                                    // 确保子分类展开
                                    const subcategoriesEl = document.getElementById(`subcategories-${categoryId}`);
                                    const chevronEl = document.getElementById(`chevron-${categoryId}`);
                                    if (subcategoriesEl?.classList.contains('hidden')) {
                                        subcategoriesEl.classList.remove('hidden');
                                        chevronEl.style.transform = 'rotate(180deg)';
                                    }
                                } else {
                                    item.classList.remove('active');
                                }
                            });
                        }
                        
                        ticking = false;
                    });
                    ticking = true;
                }
            });
        }

        // 更新点击次数
        async function incrementClicks(bookmarkId) {
            try {
                await fetch(`/api/bookmarks/${bookmarkId}/click`, {
                    method: 'POST'
                });
            } catch (error) {
                console.error('更新点击次数失败:', error);
            }
        }

        // 在 handleCategoryClick 函数之前添加 toggleSubcategories 函数
        function toggleSubcategories(categoryId) {
            const subcategoriesEl = document.getElementById(`subcategories-${categoryId}`);
            const chevronEl = document.getElementById(`chevron-${categoryId}`);
            
            if (subcategoriesEl && chevronEl) {
                const isHidden = subcategoriesEl.classList.contains('hidden');
                
                // 切换显示/隐藏状态
                if (isHidden) {
                    subcategoriesEl.classList.remove('hidden');
                    chevronEl.style.transform = 'rotate(180deg)';
                } else {
                    subcategoriesEl.classList.add('hidden');
                    chevronEl.style.transform = 'rotate(0deg)';
                }
            }
        }

        // 修改 handleCategoryClick 函数
        function handleCategoryClick(event, categoryId) {
            event.preventDefault();
            
            // 切换子分类的显示/隐藏
            toggleSubcategories(categoryId);
            
            const section = document.getElementById(`category-${categoryId}`);
            if (section) {
                // 计算精确的滚动位置
                const headerHeight = 64; // 顶部导航栏高度
                const elementPosition = section.offsetTop;
                
                // 使用 scrollTo 滚动到指定位置
                document.querySelector('.main-content').scrollTo({
                    top: elementPosition - headerHeight,
                    behavior: 'smooth'
                });

                // 更新导航项的选中状态
                const navItems = document.querySelectorAll('.category-nav-item');
                navItems.forEach(item => {
                    if (item.getAttribute('data-category-id') === categoryId.toString()) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            }
        }



        // 修改��动到子分类的函数
        function scrollToSubcategory(event, categoryId, subcategoryId) {
            event.preventDefault();
            
            const categorySection = document.getElementById(`category-${categoryId}`);
            const tabs = categorySection.querySelectorAll('.subcategory-tab');
            let activeIndex = 0;
            
            tabs.forEach((tab, index) => {
                if (parseInt(tab.getAttribute('data-subcategory-id')) === subcategoryId) {
                    activeIndex = index;
                }
            });

            // 切换到对应的子分类
            switchSubcategory(categoryId, activeIndex, subcategoryId);

            // 滚动到对应位置
            const section = document.getElementById(`subcategory-${subcategoryId}`);
            if (section) {
                const headerHeight = 80; // 增加头部偏移量
                const elementPosition = section.getBoundingClientRect().top + window.scrollY;
                const offsetPosition = elementPosition - headerHeight;

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        }

        // 页面加载时执行
        document.addEventListener('DOMContentLoaded', loadCategories);

        // 移除快照相关函数，现在使用favicon系统

        // 移除processScreenshot函数，已不需要

        // 移除所有快照相关函数，现在使用favicon系统

        // 移除快照服务配置，现在使用favicon系统

        // 移除快照错误处理函数

        // 移除快照服务检查函数

        // 移除快照调试函数

        // 移除重复的debugSearch函数

        // 测试搜索功能
        function testSearch(keyword = 'google') {
            console.log(`🧪 测试搜索功能，关键词: ${keyword}`);
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = keyword;
                const event = { target: searchInput };
                handleSearch(event);
            } else {
                console.error('❌ 搜索框未找到');
            }
        }

        // 在控制台暴露调试函数
        window.debugTools = {
            search: debugSearch,
            testSearch: testSearch,
            favicon: {
                cache: faviconCache,
                reload: loadAllFavicons,
                clear: () => faviconCache.clear(),
                stats: () => {
                    const stats = faviconCache.getStats();
                    console.log('📊 Favicon缓存统计:');
                    console.log(`总数: ${stats.total}`);
                    console.log(`✅ 成功: ${stats.success}`);
                    console.log(`❌ 失败: ${stats.failed}`);
                    console.log(`📈 成功率: ${stats.successRate}`);
                    return stats;
                }
            }
        };

        // 后台管理跳转函数
        function goToAdmin() {
            // 跳转到后台管理页面
            window.location.href = '/admin';
        }

        // 在页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            loadCategories().then(() => {
                console.log('✅ 页面加载完成，使用新的图标+文字卡片布局');

                // 异步加载所有favicon
                setTimeout(() => {
                    loadAllFavicons();
                }, 1000);
            });
        });

        // 加载所有favicon
        function loadAllFavicons() {
            const bookmarkCards = document.querySelectorAll('.bookmark-card');
            console.log(`🎨 开始异步加载 ${bookmarkCards.length} 个favicon`);

            bookmarkCards.forEach((card, index) => {
                const link = card.href;
                if (link) {
                    try {
                        const url = new URL(link);
                        const domain = url.hostname;
                        const bookmarkId = card.onclick.toString().match(/incrementClicks\((\d+)\)/)?.[1];

                        if (bookmarkId && domain) {
                            // 延迟加载，避免同时请求太多
                            setTimeout(() => {
                                loadFavicon(parseInt(bookmarkId), domain);
                            }, index * 200);
                        }
                    } catch (e) {
                        console.warn('无法解析URL:', link, e);
                    }
                }
            });
        }

        // 添加防抖函数
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // 切换清除按钮显示
        function toggleClearButton() {
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.getElementById('clearSearchBtn');

            if (searchInput.value.trim()) {
                clearBtn.classList.remove('hidden');
            } else {
                clearBtn.classList.add('hidden');
            }
        }

        // 调试搜索功能
        function debugSearch() {
            const categories = document.querySelectorAll('.category-section');
            const subcategories = document.querySelectorAll('.subcategory');
            const cards = document.querySelectorAll('.bookmark-card');

            console.log('🔍 搜索调试信息:');
            console.log(`分类数量: ${categories.length}`);
            console.log(`子分类数量: ${subcategories.length}`);
            console.log(`书签卡片数量: ${cards.length}`);

            if (cards.length > 0) {
                const firstCard = cards[0];
                const titleEl = firstCard.querySelector('h3');
                const descEl = firstCard.querySelector('p');
                console.log('第一个卡片示例:');
                console.log('- 标题:', titleEl ? titleEl.textContent : '未找到');
                console.log('- 描述:', descEl ? descEl.textContent : '未找到');
            }

            return { categories: categories.length, subcategories: subcategories.length, cards: cards.length };
        }

        // 清除搜索
        function clearSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('keyup'));
            toggleClearButton();
            searchInput.focus();
        }

        // 搜索处理函数 - 修复为只显示匹配的内容
        function handleSearch(event) {
            try {
                const searchTerm = event.target.value.toLowerCase().trim();
                const noResultsMessage = document.getElementById('noResultsMessage');

                console.log('🔍 搜索关键词:', searchTerm);

                // 移除之前的无结果消息
                if (noResultsMessage) noResultsMessage.remove();

                if (!searchTerm) {
                    // 恢复所有内容显示
                    document.querySelectorAll('.category-section').forEach(section => {
                        section.style.display = '';
                    });
                    document.querySelectorAll('.subcategory').forEach(subcategory => {
                        subcategory.style.display = '';
                    });
                    document.querySelectorAll('.bookmark-card').forEach(card => {
                        card.style.display = '';
                    });
                    console.log('✅ 清空搜索，恢复所有内容显示');
                    return;
                }

                let hasResults = false;
                const categoriesContainer = document.getElementById('categories');

                // 遍历所有分类
                document.querySelectorAll('.category-section').forEach(categorySection => {
                    let categoryHasResults = false;

                    // 遍历该分类下的所有子分类
                    categorySection.querySelectorAll('.subcategory').forEach(subcategory => {
                        let subcategoryHasResults = false;

                        // 遍历该子分类下的所有书签卡片
                        subcategory.querySelectorAll('.bookmark-card').forEach(card => {
                            try {
                                const titleEl = card.querySelector('h3');
                                const descEl = card.querySelector('p');

                                if (!titleEl || !descEl) return;

                                const title = titleEl.textContent.toLowerCase();
                                const description = descEl.textContent.toLowerCase();
                                const url = card.href || '';

                                // 模糊搜索：标题、描述、URL
                                const isMatch = title.includes(searchTerm) ||
                                              description.includes(searchTerm) ||
                                              url.toLowerCase().includes(searchTerm);

                                if (isMatch) {
                                    card.style.display = '';
                                    subcategoryHasResults = true;
                                    categoryHasResults = true;
                                    hasResults = true;
                                    console.log('✅ 找到匹配:', title);
                                } else {
                                    card.style.display = 'none';
                                }
                            } catch (error) {
                                console.error('搜索处理单个卡片时出错:', error);
                            }
                        });

                        // 如果子分类有匹配结果，显示子分类，否则隐藏
                        subcategory.style.display = subcategoryHasResults ? '' : 'none';
                    });

                    // 如果分类有匹配结果，显示分类，否则隐藏
                    categorySection.style.display = categoryHasResults ? '' : 'none';
                });

                console.log('🔍 搜索完成，有结果:', hasResults);

                // 如果没有任何结果，显示无结果消息
                if (!hasResults) {
                    const message = document.createElement('div');
                    message.id = 'noResultsMessage';
                    message.className = 'text-center py-16 text-gray-600';
                    message.innerHTML = `
                        <i class="mdi mdi-file-search-outline text-6xl mb-4 text-gray-400"></i>
                        <p class="text-xl font-medium mb-2">未找到匹配的书签</p>
                        <p class="text-sm text-gray-500">尝试使用其他关键词搜索，或者<button onclick="clearSearch()" class="text-blue-500 hover:text-blue-600 underline">清除搜索</button></p>
                        <p class="text-xs text-gray-400 mt-2">搜索范围：书签标题、描述、网址</p>
                    `;
                    if (categoriesContainer) {
                        categoriesContainer.appendChild(message);
                    }
                }
            } catch (error) {
                console.error('搜索功能出错:', error);
            }
        }

        // 添加快捷键支持
        document.addEventListener('keydown', (event) => {
            // 按下 Ctrl + K 或 Command + K 时聚焦搜索框
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                document.getElementById('searchInput').focus();
            }
            // 按下 Esc 键时清空搜索框并恢复显示
            if (event.key === 'Escape') {
                const searchInput = document.getElementById('searchInput');
                if (searchInput === document.activeElement) {
                    clearSearch();
                    searchInput.blur();
                }
            }
        });

        // 在搜索框上方添加快捷键提示
        document.addEventListener('DOMContentLoaded', () => {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.setAttribute('title', '快捷键: Ctrl + K / Command + K');
            }
        });

        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 点击主内容区关闭侧边栏（移动端）
        document.addEventListener('click', (event) => {
            const sidebar = document.getElementById('sidebar');
            const isClickInsideSidebar = sidebar.contains(event.target);
            const isMenuButton = event.target.closest('[onclick="toggleSidebar()"]');

            if (!isClickInsideSidebar && !isMenuButton && window.innerWidth <= 768) {
                sidebar.classList.remove('open');
            }
        });

        // 在 mockData 后添加 loadCategories 函数
        async function loadCategories() {
            const loadingEl = document.getElementById('loading');
            try {
                // 使用实际的 API
                const response = await fetch('/api/categories');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (!data.success) {
                    throw new Error(data.message || '获取数据失败');
                }

                if (data.success && data.data && Array.isArray(data.data)) {
                    // 加载左侧导航
                    renderCategoryNav(data.data);

                    // 加载主内容区
                    const categoriesContainer = document.getElementById('categories');
                    if (categoriesContainer) {
                        categoriesContainer.innerHTML = ''; // 清除加载提示

                        data.data.forEach(category => {
                            if (!category.category_id || !category.category_title) {
                                console.warn('无效的分类数据:', category);
                                return;
                            }

                            const categorySection = document.createElement('section');
                            categorySection.id = `category-${category.category_id}`;
                            categorySection.className = 'category-section mb-20';

                            const subcategoriesCount = category.subcategories ? category.subcategories.length : 0;
                            const bookmarksCount = category.subcategories ?
                                category.subcategories.reduce((total, sub) => total + (sub.links ? sub.links.length : 0), 0) : 0;

                            categorySection.innerHTML = `
                                <div class="glass-effect rounded-2xl p-8 mb-8">
                                    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
                                        <div class="flex items-center space-x-4 mb-4 lg:mb-0">
                                            <div>
                                                <h2 class="text-2xl font-bold mb-1 category-main-title">
                                                    ${category.category_title}
                                                </h2>
                                                <p class="text-sm category-subtitle">
                                                    ${subcategoriesCount} 个分类 · ${bookmarksCount} 个书签
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex flex-wrap gap-3">
                                            ${category.subcategories ? renderSubcategoryTabs(category, category.subcategories) : ''}
                                        </div>
                                    </div>
                                    <div class="category-content">
                                        ${category.subcategories ? renderSubcategories(category.subcategories) : '<p class="text-gray-600">暂无书签</p>'}
                                    </div>
                                </div>
                            `;

                            categoriesContainer.appendChild(categorySection);
                        });

                        // 添加滚动监听
                        setupScrollSpy();

                        // 移除加载提示
                        if (loadingEl) {
                            loadingEl.remove();
                        }
                    }
                } else {
                    throw new Error('返回的数据格式不正确');
                }
            } catch (error) {
                console.error('加载分类失败:', error);
                if (loadingEl) {
                    loadingEl.innerHTML = `
                        <div class="text-center py-16">
                            <div class="text-red-600">
                                <i class="mdi mdi-alert-circle text-4xl mb-4"></i>
                                <p class="text-lg font-medium mb-2">加载失败</p>
                                <p class="text-sm text-red-500 mb-4">${error.message}</p>
                                <button onclick="location.reload()"
                                        class="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">
                                    重新加载
                                </button>
                            </div>
                        </div>
                    `;
                }
            }
        }

        // 添加渲染分类导航函数
        function renderCategoryNav(categories) {
            const categoryNav = document.getElementById('category-nav');
            categoryNav.innerHTML = categories.map(category => `
                <div class="category-nav-item mb-3" data-category-id="${category.category_id}">
                    <div class="w-full flex items-center justify-between p-4 rounded-xl cursor-pointer transition-all duration-300"
                         onclick="handleCategoryClick(event, ${category.category_id})">
                        <div class="flex items-center space-x-3">
                            <span class="font-medium">${category.category_title}</span>
                        </div>
                        <i class="mdi mdi-chevron-down transition-transform text-icon"
                           id="chevron-${category.category_id}"></i>
                    </div>
                    <div class="subcategories ml-6 mt-2 space-y-1 hidden" id="subcategories-${category.category_id}">
                        ${category.subcategories.map(subcat => `
                            <a href="#subcategory-${subcat.subcategory_id}"
                               onclick="scrollToSubcategory(event, ${category.category_id}, ${subcat.subcategory_id})"
                               class="flex items-center p-3 rounded-lg text-sm nav-subcategory-link transition-all duration-300"
                               data-category-id="${category.category_id}"
                               data-subcategory-id="${subcat.subcategory_id}">
                                <span>${subcat.subcategory_title}</span>
                            </a>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }

        // 添加子分类标签渲染函数
        function renderSubcategoryTabs(category, subcategories) {
            return subcategories.map((subcat, index) => `
                <button onclick="switchSubcategory(${category.category_id}, ${index}, ${subcat.subcategory_id})"
                        class="subcategory-tab px-4 py-2 rounded-lg text-sm font-medium transition-colors
                               ${index === 0 ? 'active' : ''}"
                        data-subcategory-id="${subcat.subcategory_id}">
                    <span>${subcat.subcategory_title}</span>
                </button>
            `).join('');
        }

        // 添加 switchSubcategory 函数
        function switchSubcategory(categoryId, activeIndex, subcategoryId) {
            // 更新右侧标签状态
            const categorySection = document.getElementById(`category-${categoryId}`);
            const tabs = categorySection.querySelectorAll('.subcategory-tab');

            tabs.forEach((tab, index) => {
                if (index === activeIndex) {
                    tab.classList.add('active');
                } else {
                    tab.classList.remove('active');
                }
            });

            // 更新左侧导航状态
            const navSubcategoryLinks = document.querySelectorAll(`.nav-subcategory-link[data-category-id="${categoryId}"]`);
            navSubcategoryLinks.forEach(link => {
                if (parseInt(link.getAttribute('data-subcategory-id')) === subcategoryId) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });

            // 显示对应的子分类内容
            const subcategories = categorySection.querySelectorAll('.subcategory');
            subcategories.forEach((subcategory, index) => {
                if (index === activeIndex) {
                    subcategory.style.display = '';
                } else {
                    subcategory.style.display = 'none';
                }
            });

            // 确保左侧子分类列表展开
            const subcategoriesEl = document.getElementById(`subcategories-${categoryId}`);
            const chevronEl = document.getElementById(`chevron-${categoryId}`);
            if (subcategoriesEl?.classList.contains('hidden')) {
                subcategoriesEl.classList.remove('hidden');
                chevronEl.style.transform = 'rotate(180deg)';
            }
        }
    </script>
</body>
</html>