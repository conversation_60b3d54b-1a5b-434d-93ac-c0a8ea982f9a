<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 书签导航</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="manifest" href="/site.webmanifest">
    <meta name="theme-color" content="#667eea">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.2.96/css/materialdesignicons.min.css">
    <style>
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .input-group {
            position: relative;
        }
        
        .input-group input {
            transition: all 0.3s ease;
        }
        
        .input-group input:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container flex items-center justify-center p-4">
        <div class="login-card rounded-2xl p-8 w-full max-w-md">
            <!-- 标题 -->
            <div class="text-center mb-8">
                <div class="text-4xl mb-4">🔐</div>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">管理员登录</h1>
                <p class="text-gray-600">请输入管理员密码</p>
            </div>

            <!-- 错误提示 -->
            <div id="error-alert" class="alert hidden mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <i class="mdi mdi-alert-circle text-red-500 mr-2"></i>
                    <span id="error-message" class="text-red-700"></span>
                </div>
            </div>

            <!-- 登录表单 -->
            <form id="login-form" class="space-y-6">
                <div class="input-group">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="mdi mdi-lock mr-1"></i>
                        密码
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            required
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none pr-12"
                            placeholder="请输入密码"
                        >
                        <button 
                            type="button" 
                            id="toggle-password"
                            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                        >
                            <i class="mdi mdi-eye" id="password-icon"></i>
                        </button>
                    </div>
                </div>

                <button 
                    type="submit" 
                    id="login-btn"
                    class="btn-login w-full py-3 px-4 text-white font-medium rounded-lg hover:shadow-lg transition-all duration-300"
                >
                    <span id="login-text">
                        <i class="mdi mdi-login mr-2"></i>
                        登录
                    </span>
                    <span id="login-loading" class="hidden">
                        <i class="mdi mdi-loading mdi-spin mr-2"></i>
                        登录中...
                    </span>
                </button>
            </form>



            <!-- 返回首页 -->
            <div class="mt-8 text-center">
                <a 
                    href="/" 
                    class="inline-flex items-center text-gray-600 hover:text-gray-800 text-sm transition-colors"
                >
                    <i class="mdi mdi-arrow-left mr-1"></i>
                    返回首页
                </a>
            </div>
        </div>
    </div>



    <script src="login.js"></script>
</body>
</html>
