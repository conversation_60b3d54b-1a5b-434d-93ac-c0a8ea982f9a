// 登录页面JavaScript



document.addEventListener('DOMContentLoaded', () => {
    // 绑定事件
    bindEvents();
});

function bindEvents() {
    // 登录表单提交
    document.getElementById('login-form').addEventListener('submit', handleLogin);
    
    // 密码显示/隐藏
    document.getElementById('toggle-password').addEventListener('click', togglePassword);
    

}



// 处理登录
async function handleLogin(e) {
    e.preventDefault();

    const password = document.getElementById('password').value;

    if (!password) {
        showError('请输入密码');
        return;
    }
    
    setLoginLoading(true);
    hideError();
    
    try {
        const response = await fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin', // 固定用户名
                password,
                userAgent: navigator.userAgent
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            // 登录成功，保存会话并跳转
            if (result.sessionId) {
                // 同时保存到Cookie和localStorage
                document.cookie = `sessionId=${result.sessionId}; path=/; max-age=86400`; // 24小时
                localStorage.setItem('sessionId', result.sessionId);
            }

            // 跳转到后台管理
            window.location.href = '/admin';
        } else {
            showError(result.message || '登录失败');
        }
    } catch (error) {
        console.error('登录请求失败:', error);
        showError('登录请求失败，请检查网络连接');
    } finally {
        setLoginLoading(false);
    }
}

// 切换密码显示/隐藏
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const passwordIcon = document.getElementById('password-icon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'mdi mdi-eye-off';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'mdi mdi-eye';
    }
}





// 设置登录按钮加载状态
function setLoginLoading(loading) {
    const btn = document.getElementById('login-btn');
    const text = document.getElementById('login-text');
    const loadingText = document.getElementById('login-loading');
    
    if (loading) {
        btn.disabled = true;
        text.classList.add('hidden');
        loadingText.classList.remove('hidden');
    } else {
        btn.disabled = false;
        text.classList.remove('hidden');
        loadingText.classList.add('hidden');
    }
}



// 显示错误信息
function showError(message) {
    const errorAlert = document.getElementById('error-alert');
    const errorMessage = document.getElementById('error-message');
    
    errorMessage.textContent = message;
    errorAlert.classList.remove('hidden');
}

// 隐藏错误信息
function hideError() {
    const errorAlert = document.getElementById('error-alert');
    errorAlert.classList.add('hidden');
}
