const express = require('express');
const router = express.Router();
const db = require('../utils/mysqlDb');

// 获取所有分类
router.get('/categories', async (req, res, next) => {
    try {
        const result = await db.execute(`
            SELECT * FROM categories
            ORDER BY category_sort ASC
        `);
        const rows = result[0];
        res.json({ success: true, data: rows });
    } catch (error) {
        next(error);
    }
});

// 添加分类
router.post('/categories', async (req, res, next) => {
    try {
        const { category_title, category_icon, category_color, category_sort } = req.body;

        if (!category_title) {
            return res.status(400).json({ success: false, message: '分类标题不能为空' });
        }

        const insertResult = await db.execute(`
            INSERT INTO categories (category_title, category_icon, category_color, category_sort)
            VALUES (?, ?, ?, ?)
        `, [category_title, category_icon || 'mdi-folder', category_color || 'text-blue-600', category_sort || 0]);
        const result = insertResult[0];

        res.json({ success: true, data: { category_id: result.insertId } });
    } catch (error) {
        next(error);
    }
});

// 更新分类
router.put('/categories/:id', async (req, res, next) => {
    try {
        const categoryId = parseInt(req.params.id);
        const { category_title, category_icon, category_color, category_sort } = req.body;

        if (!categoryId || isNaN(categoryId)) {
            return res.status(400).json({ success: false, message: '无效的分类ID' });
        }

        await db.execute(`
            UPDATE categories
            SET category_title = ?, category_icon = ?, category_color = ?, category_sort = ?
            WHERE category_id = ?
        `, [category_title, category_icon || '', category_color || '', category_sort || 0, categoryId]);

        res.json({ success: true });
    } catch (error) {
        next(error);
    }
});

// 删除分类
router.delete('/categories/:id', async (req, res, next) => {
    try {
        const categoryId = parseInt(req.params.id);

        if (!categoryId || isNaN(categoryId)) {
            return res.status(400).json({ success: false, message: '无效的分类ID' });
        }

        // 软删除分类
        await db.execute(`
            DELETE FROM categories WHERE category_id = ?
        `, [categoryId]);

        res.json({ success: true });
    } catch (error) {
        next(error);
    }
});

// 获取子分类
router.get('/subcategories', async (req, res, next) => {
    try {
        const categoryId = req.query.category_id;
        let query = `
            SELECT s.*, c.category_title
            FROM subcategories s
            LEFT JOIN categories c ON s.category_id = c.category_id
        `;
        let params = [];

        if (categoryId) {
            query += ' WHERE s.category_id = ?';
            params.push(categoryId);
        }

        query += ' ORDER BY s.subcategory_sort ASC';

        const queryResult = await db.execute(query, params);
        const rows = queryResult[0];
        res.json({ success: true, data: rows });
    } catch (error) {
        next(error);
    }
});

// 添加子分类
router.post('/subcategories', async (req, res, next) => {
    try {
        const { category_id, subcategory_title, subcategory_icon, subcategory_color, subcategory_sort } = req.body;

        if (!category_id || !subcategory_title) {
            return res.status(400).json({ success: false, message: '分类ID和子分类标题不能为空' });
        }

        const insertResult = await db.execute(`
            INSERT INTO subcategories (category_id, subcategory_title, subcategory_icon, subcategory_color, subcategory_sort)
            VALUES (?, ?, ?, ?, ?)
        `, [category_id, subcategory_title, subcategory_icon || 'mdi-folder-outline', subcategory_color || 'text-green-600', subcategory_sort || 0]);
        const result = insertResult[0];

        res.json({ success: true, data: { subcategory_id: result.insertId } });
    } catch (error) {
        next(error);
    }
});

// 更新子分类
router.put('/subcategories/:id', async (req, res, next) => {
    try {
        const subcategoryId = parseInt(req.params.id);
        const { category_id, subcategory_title, subcategory_icon, subcategory_color, subcategory_sort } = req.body;

        if (!subcategoryId || isNaN(subcategoryId)) {
            return res.status(400).json({ success: false, message: '无效的子分类ID' });
        }

        await db.execute(`
            UPDATE subcategories
            SET category_id = ?, subcategory_title = ?, subcategory_icon = ?, subcategory_color = ?, subcategory_sort = ?
            WHERE subcategory_id = ?
        `, [
            category_id,
            subcategory_title,
            subcategory_icon || '',
            subcategory_color || '',
            subcategory_sort || 0,
            subcategoryId
        ]);

        res.json({ success: true });
    } catch (error) {
        next(error);
    }
});

// 删除子分类
router.delete('/subcategories/:id', async (req, res, next) => {
    try {
        const subcategoryId = parseInt(req.params.id);

        if (!subcategoryId || isNaN(subcategoryId)) {
            return res.status(400).json({ success: false, message: '无效的子分类ID' });
        }

        // 软删除子分类
        await db.execute(`
            DELETE FROM subcategories WHERE subcategory_id = ?
        `, [subcategoryId]);

        res.json({ success: true });
    } catch (error) {
        next(error);
    }
});

// 获取书签
router.get('/bookmarks', async (req, res, next) => {
    try {
        const { category_id, subcategory_id, page = 1, limit = 20 } = req.query;
        const offset = (page - 1) * limit;

        let query = `
            SELECT b.*, s.subcategory_title, c.category_title
            FROM bookmarks b
            LEFT JOIN subcategories s ON b.subcategory_id = s.subcategory_id
            LEFT JOIN categories c ON s.category_id = c.category_id
        `;
        let params = [];

        if (subcategory_id) {
            query += ' WHERE b.subcategory_id = ?';
            params.push(subcategory_id);
        } else if (category_id) {
            query += ' WHERE s.category_id = ?';
            params.push(category_id);
        }

        query += ` ORDER BY b.bookmark_sort ASC, b.click_count DESC LIMIT ${parseInt(limit)} OFFSET ${parseInt(offset)}`;

        const result = await db.execute(query, params);
        const rows = result[0];

        // 获取总数
        let countQuery = `
            SELECT COUNT(*) as total
            FROM bookmarks b
            LEFT JOIN subcategories s ON b.subcategory_id = s.subcategory_id
        `;
        let countParams = [];

        if (subcategory_id) {
            countQuery += ' WHERE b.subcategory_id = ?';
            countParams.push(subcategory_id);
        } else if (category_id) {
            countQuery += ' WHERE s.category_id = ?';
            countParams.push(category_id);
        }

        const countResultRaw = await db.execute(countQuery, countParams);
        const countResult = countResultRaw[0];
        const total = countResult[0].total;

        res.json({
            success: true,
            data: rows,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('❌ 书签API错误:', error);
        res.status(500).json({
            success: false,
            message: '书签数据加载失败: ' + error.message
        });
    }
});

// 添加书签
router.post('/bookmarks', async (req, res, next) => {
    try {
        const { 
            subcategory_id, 
            bookmark_title, 
            bookmark_url, 
            bookmark_description, 
            bookmark_icon, 
            bookmark_sort 
        } = req.body;
        
        if (!subcategory_id || !bookmark_title || !bookmark_url) {
            return res.status(400).json({ 
                success: false, 
                message: '子分类ID、书签标题和URL不能为空' 
            });
        }

        const insertResult = await db.execute(`
            INSERT INTO bookmarks (subcategory_id, bookmark_title, bookmark_url, bookmark_description, bookmark_icon, bookmark_sort)
            VALUES (?, ?, ?, ?, ?, ?)
        `, [subcategory_id, bookmark_title, bookmark_url, bookmark_description || '', bookmark_icon || '', bookmark_sort || 0]);
        const result = insertResult[0];

        res.json({ success: true, data: { bookmark_id: result.insertId } });
    } catch (error) {
        next(error);
    }
});

// 更新书签
router.put('/bookmarks/:id', async (req, res, next) => {
    try {
        const bookmarkId = parseInt(req.params.id);
        const { 
            subcategory_id, 
            bookmark_title, 
            bookmark_url, 
            bookmark_description, 
            bookmark_icon, 
            bookmark_sort 
        } = req.body;

        if (!bookmarkId || isNaN(bookmarkId)) {
            return res.status(400).json({ success: false, message: '无效的书签ID' });
        }

        await db.execute(`
            UPDATE bookmarks
            SET subcategory_id = ?, bookmark_title = ?, bookmark_url = ?,
                bookmark_description = ?, bookmark_icon = ?, bookmark_sort = ?
            WHERE bookmark_id = ?
        `, [
            subcategory_id,
            bookmark_title,
            bookmark_url,
            bookmark_description || '',
            bookmark_icon || '',
            bookmark_sort || 0,
            bookmarkId
        ]);

        res.json({ success: true });
    } catch (error) {
        next(error);
    }
});

// 删除书签
router.delete('/bookmarks/:id', async (req, res, next) => {
    try {
        const bookmarkId = parseInt(req.params.id);

        if (!bookmarkId || isNaN(bookmarkId)) {
            return res.status(400).json({ success: false, message: '无效的书签ID' });
        }

        // 软删除书签
        await db.execute(`
            DELETE FROM bookmarks WHERE bookmark_id = ?
        `, [bookmarkId]);

        res.json({ success: true });
    } catch (error) {
        next(error);
    }
});

// 获取统计信息
router.get('/stats', async (req, res, next) => {
    try {
        const categoriesResult = await db.execute('SELECT COUNT(*) as count FROM categories ');
        const subcategoriesResult = await db.execute('SELECT COUNT(*) as count FROM subcategories ');
        const bookmarksResult = await db.execute('SELECT COUNT(*) as count FROM bookmarks ');
        const totalClicksResult = await db.execute('SELECT SUM(click_count) as total FROM bookmarks ');

        const categories = categoriesResult[0];
        const subcategories = subcategoriesResult[0];
        const bookmarks = bookmarksResult[0];
        const totalClicks = totalClicksResult[0];

        res.json({
            success: true,
            data: {
                categories: categories[0].count,
                subcategories: subcategories[0].count,
                bookmarks: bookmarks[0].count,
                totalClicks: totalClicks[0].total || 0
            }
        });
    } catch (error) {
        next(error);
    }
});

module.exports = router;
