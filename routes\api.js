const express = require('express');
const router = express.Router();
const db = require('../utils/mysqlDb');

// 获取所有分类和书签
router.get('/categories', async (req, res, next) => {
    console.log('📡 收到API请求: /api/categories');
    try {
        // 使用 mysqlDb 的 getCategories 方法
        const categories = await db.getCategories();

        if (!categories || !Array.isArray(categories)) {
            throw new Error('数据库查询返回无效数据');
        }

        console.log(`✅ 成功返回 ${categories.length} 个分类的数据`);

        res.json({
            success: true,
            data: categories,
            count: categories.length
        });
    } catch (error) {
        console.error('获取分类数据失败:', error);
        next(error);
    }
});

// 更新书签点击次数
router.post('/bookmarks/:id/click', async (req, res, next) => {
    try {
        const bookmarkId = parseInt(req.params.id);

        // 验证书签ID
        if (!bookmarkId || isNaN(bookmarkId)) {
            return res.status(400).json({
                success: false,
                message: '无效的书签ID'
            });
        }

        // 使用 mysqlDb 的 incrementBookmarkClicks 方法
        const updatedBookmark = await db.incrementBookmarkClicks(bookmarkId);

        if (!updatedBookmark) {
            return res.status(404).json({
                success: false,
                message: '书签不存在'
            });
        }

        res.json({ success: true });
    } catch (error) {
        console.error('更新点击次数失败:', error);
        next(error);
    }
});

module.exports = router; 