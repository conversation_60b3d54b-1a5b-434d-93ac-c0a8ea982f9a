const express = require('express');
const bcrypt = require('bcrypt');
const db = require('../utils/mysqlDb');
const crypto = require('crypto');

const router = express.Router();

// 简单的会话存储（内存中）
const sessions = new Map();

// 生成会话ID
function generateSessionId() {
    return crypto.randomBytes(64).toString('hex');
}

// 创建会话
function createSession(username) {
    const sessionId = generateSessionId();
    const session = {
        username,
        createdAt: new Date(),
        lastActivity: new Date()
    };
    sessions.set(sessionId, session);
    return sessionId;
}

// 验证会话
function verifySession(sessionId) {
    const session = sessions.get(sessionId);
    if (session) {
        session.lastActivity = new Date();
        return session;
    }
    return null;
}

// 销毁会话
function destroySession(sessionId) {
    return sessions.delete(sessionId);
}

// 获取客户端IP地址
function getClientIP(req) {
    return req.headers['x-forwarded-for'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           '127.0.0.1';
}





// 用户登录
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        const ipAddress = getClientIP(req);
        const userAgent = req.headers['user-agent'];

        // 验证输入
        if (!username || !password) {
            return res.status(400).json({
                success: false,
                message: '用户名和密码为必填项'
            });
        }

        // 移除账户锁定检查

        // 验证管理员账户
        const adminUsername = 'admin'; // 固定用户名
        const adminPassword = process.env.ADMIN_PASSWORD;

        if (!adminPassword) {
            console.error('ADMIN_PASSWORD 环境变量未设置');
            return res.status(500).json({
                success: false,
                message: '系统配置错误'
            });
        }

        if (username === adminUsername) {
            // 验证密码
            if (password !== adminPassword) {
                return res.status(401).json({
                    success: false,
                    message: '密码错误'
                });
            }

            // 创建会话
            const sessionId = createSession(adminUsername);

            // 设置会话cookie
            res.cookie('sessionId', sessionId, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                maxAge: 24 * 60 * 60 * 1000 // 24小时
            });

            res.json({
                success: true,
                message: '登录成功',
                user: {
                    username: adminUsername,
                    userType: 'admin'
                },
                sessionId: sessionId
            });

        } else {
            // 用户名不匹配
            return res.status(401).json({
                success: false,
                message: '用户名或密码错误'
            });
        }

    } catch (error) {
        console.error('登录失败:', error);
        res.status(500).json({
            success: false,
            message: '登录服务错误'
        });
    }
});

// 用户登出
router.post('/logout', async (req, res) => {
    try {
        const sessionId = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.sessionId;

        if (sessionId) {
            destroySession(sessionId);
        }

        // 清除cookie
        res.clearCookie('sessionId');

        res.json({
            success: true,
            message: '登出成功'
        });

    } catch (error) {
        console.error('登出失败:', error);
        res.status(500).json({
            success: false,
            message: '登出服务错误'
        });
    }
});

// 验证会话
router.get('/verify', (req, res) => {
    const sessionId = req.headers.authorization?.replace('Bearer ', '') || req.cookies?.sessionId;

    if (!sessionId) {
        return res.status(401).json({
            success: false,
            message: '未登录'
        });
    }

    const session = verifySession(sessionId);
    if (!session) {
        return res.status(401).json({
            success: false,
            message: '会话已过期'
        });
    }

    res.json({
        success: true,
        user: {
            username: session.username,
            userType: 'admin'
        }
    });
});









module.exports = router;
