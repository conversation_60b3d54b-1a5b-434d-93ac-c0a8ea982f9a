require('dotenv').config();
const express = require('express');
const path = require('path');
const cookieParser = require('cookie-parser');
const apiRoutes = require('./routes/api');
const adminRoutes = require('./routes/admin');
const authRoutes = require('./routes/auth');

const errorHandler = require('./middleware/errorHandler');

const app = express();

// 中间件
app.use(express.json());
app.use(cookieParser());
app.use(express.static('public'));



// API 路由
app.use('/api', apiRoutes);

// 认证路由
app.use('/api/auth', authRoutes);



// 后台管理路由（移除认证）
app.use('/admin/api', adminRoutes);

// 前端路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 登录页面路由
app.get('/login', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'login.html'));
});



// 后台管理页面路由（简化认证）
app.get('/admin', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'admin.html'));
});

// 404 处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '页面未找到'
    });
});

// 错误处理中间件
app.use(errorHandler);

// 启动服务器
const port = process.env.PORT || 3001;
console.log('🔧 正在启动服务器...');
console.log('📋 环境配置:', {
    port,
    nodeEnv: process.env.NODE_ENV,
    dbHost: process.env.DB_HOST,
    dbName: process.env.DB_NAME
});

app.listen(port, () => {
    console.log(`🚀 Server is running on port ${port}`);
    console.log(`📱 访问地址: http://localhost:${port}`);

    console.log('🧹 认证系统已简化');
}).on('error', (err) => {
    console.error('❌ 服务器启动失败:', err.message);
    process.exit(1);
});