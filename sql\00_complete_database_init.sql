-- 完整数据库初始化脚本
-- 执行顺序：先执行此文件，然后按顺序执行其他文件
-- 注意：不使用外键约束，采用逻辑关联

-- 设置字符集和排序规则
SET NAMES utf8mb4;

-- ================================
-- 1. 书签系统核心表
-- ================================

-- 主分类表
DROP TABLE IF EXISTS categories;
CREATE TABLE categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_title VARCHAR(100) NOT NULL,
    category_icon VARCHAR(50) NULL,
    category_color VARCHAR(50) NULL,
    category_sort INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort (category_sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 子分类表
DROP TABLE IF EXISTS subcategories;
CREATE TABLE subcategories (
    subcategory_id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL COMMENT '关联主分类ID',
    subcategory_title VARCHAR(100) NOT NULL,
    subcategory_icon VARCHAR(50) NULL,
    subcategory_color VARCHAR(50) NULL,
    subcategory_sort INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_sort (category_id, subcategory_sort)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 书签表
DROP TABLE IF EXISTS bookmarks;
CREATE TABLE bookmarks (
    bookmark_id INT PRIMARY KEY AUTO_INCREMENT,
    subcategory_id INT NOT NULL COMMENT '关联子分类ID',
    bookmark_title VARCHAR(200) NOT NULL,
    bookmark_url TEXT NOT NULL,
    bookmark_description TEXT NULL,
    bookmark_icon VARCHAR(50) NULL,
    bookmark_sort INT DEFAULT 0,
    click_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL COMMENT '创建者用户ID',
    INDEX idx_subcategory_sort (subcategory_id, bookmark_sort),
    INDEX idx_click_count (click_count),
    INDEX idx_created_by (created_by)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ================================
-- 2. 管理员认证系统表
-- ================================

-- 管理员表（简化版，只保留管理员账户）
DROP TABLE IF EXISTS users;
CREATE TABLE users (
    user_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    user_type ENUM('admin') NOT NULL DEFAULT 'admin',
    is_first_login BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP NULL,
    status ENUM('active', 'locked') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- 登录尝试记录表
DROP TABLE IF EXISTS login_attempts;
CREATE TABLE login_attempts (
    attempt_id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_successful BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(100) NULL,
    user_agent TEXT NULL,
    INDEX idx_username_time (username, attempt_time),
    INDEX idx_ip_time (ip_address, attempt_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 账户锁定记录表
DROP TABLE IF EXISTS account_locks;
CREATE TABLE account_locks (
    lock_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL COMMENT '关联用户ID',
    username VARCHAR(50) NOT NULL,
    lock_reason VARCHAR(100) NOT NULL,
    locked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unlock_at TIMESTAMP NOT NULL,
    failure_count INT NOT NULL DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    unlocked_by VARCHAR(50) NULL,
    unlocked_at TIMESTAMP NULL,
    INDEX idx_username_active (username, is_active),
    INDEX idx_unlock_time (unlock_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户会话表
DROP TABLE IF EXISTS user_sessions;
CREATE TABLE user_sessions (
    session_id VARCHAR(128) PRIMARY KEY,
    user_id INT NOT NULL COMMENT '关联用户ID',
    username VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_active (user_id, is_active),
    INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 操作日志表
DROP TABLE IF EXISTS operation_logs;
CREATE TABLE operation_logs (
    log_id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL COMMENT '关联用户ID',
    username VARCHAR(50) NOT NULL,
    operation_type VARCHAR(50) NOT NULL,
    operation_detail TEXT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    result ENUM('success', 'failure', 'error') NOT NULL,
    error_message TEXT NULL,
    INDEX idx_user_time (user_id, created_at),
    INDEX idx_operation_time (operation_type, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ================================
-- 3. 访客统计系统表
-- ================================







-- 插入默认管理员账户（密码通过环境变量配置）
INSERT INTO users (username, password_hash, user_type, is_first_login)
VALUES ('admin', '', 'admin', FALSE)
ON DUPLICATE KEY UPDATE username = username;

-- ================================
-- 4. 示例数据（可选）
-- ================================

-- 插入示例分类数据
INSERT INTO categories (category_title, category_icon, category_sort) VALUES
('常用工具', 'mdi-tools', 1),
('开发资源', 'mdi-code-tags', 2),
('设计素材', 'mdi-palette', 3),
('学习教育', 'mdi-school', 4);

-- 插入示例子分类数据
INSERT INTO subcategories (category_id, subcategory_title, subcategory_icon, subcategory_sort) VALUES
(1, '搜索引擎', 'mdi-magnify', 1),
(1, '在线工具', 'mdi-wrench', 2),
(1, '云存储', 'mdi-cloud', 3),
(2, '代码托管', 'mdi-git', 1),
(2, '开发文档', 'mdi-book-open', 2),
(2, '在线编辑器', 'mdi-code-braces', 3),
(3, '图标素材', 'mdi-image', 1),
(3, '字体资源', 'mdi-format-font', 2),
(3, '配色工具', 'mdi-palette-swatch', 3),
(4, '在线课程', 'mdi-play-circle', 1),
(4, '技术博客', 'mdi-post', 2),
(4, '文档资料', 'mdi-file-document', 3);

-- 插入示例书签数据
INSERT INTO bookmarks (subcategory_id, bookmark_title, bookmark_url, bookmark_description, bookmark_sort) VALUES
-- 搜索引擎
(1, 'Google', 'https://www.google.com', '全球最大的搜索引擎', 1),
(1, '百度', 'https://www.baidu.com', '中国最大的搜索引擎', 2),
(1, 'Bing', 'https://www.bing.com', '微软搜索引擎', 3),

-- 在线工具
(2, 'JSON格式化', 'https://jsonformatter.org', '在线JSON格式化和验证工具', 1),
(2, '正则表达式测试', 'https://regex101.com', '在线正则表达式测试工具', 2),
(2, 'Base64编解码', 'https://base64decode.org', '在线Base64编解码工具', 3),

-- 云存储
(3, 'Google Drive', 'https://drive.google.com', '谷歌云存储服务', 1),
(3, '百度网盘', 'https://pan.baidu.com', '百度云存储服务', 2),
(3, 'OneDrive', 'https://onedrive.live.com', '微软云存储服务', 3),

-- 代码托管
(4, 'GitHub', 'https://github.com', '全球最大的代码托管平台', 1),
(4, 'GitLab', 'https://gitlab.com', '开源的代码托管平台', 2),
(4, 'Gitee', 'https://gitee.com', '中国的代码托管平台', 3),

-- 开发文档
(5, 'MDN Web Docs', 'https://developer.mozilla.org', 'Web开发权威文档', 1),
(5, 'Stack Overflow', 'https://stackoverflow.com', '程序员问答社区', 2),
(5, 'W3Schools', 'https://www.w3schools.com', 'Web技术教程网站', 3),

-- 在线编辑器
(6, 'CodePen', 'https://codepen.io', '前端代码在线编辑器', 1),
(6, 'JSFiddle', 'https://jsfiddle.net', 'JavaScript在线编辑器', 2),
(6, 'Repl.it', 'https://replit.com', '多语言在线编程环境', 3),

-- 图标素材
(7, 'Iconfont', 'https://www.iconfont.cn', '阿里巴巴图标库', 1),
(7, 'Font Awesome', 'https://fontawesome.com', '流行的图标字体库', 2),
(7, 'Feather Icons', 'https://feathericons.com', '简洁的开源图标集', 3),

-- 字体资源
(8, 'Google Fonts', 'https://fonts.google.com', '谷歌免费字体库', 1),
(8, '字体天下', 'https://www.fonts.net.cn', '中文字体资源网站', 2),
(8, 'DaFont', 'https://www.dafont.com', '免费字体下载网站', 3),

-- 配色工具
(9, 'Coolors', 'https://coolors.co', '在线配色方案生成器', 1),
(9, 'Adobe Color', 'https://color.adobe.com', 'Adobe配色工具', 2),
(9, 'Paletton', 'https://paletton.com', '专业配色工具', 3),

-- 在线课程
(10, 'Coursera', 'https://www.coursera.org', '国际知名在线课程平台', 1),
(10, '慕课网', 'https://www.imooc.com', '程序员在线学习平台', 2),
(10, 'edX', 'https://www.edx.org', '哈佛MIT等名校课程平台', 3),

-- 技术博客
(11, '掘金', 'https://juejin.cn', '中国技术社区', 1),
(11, 'Medium', 'https://medium.com', '国际技术博客平台', 2),
(11, 'Dev.to', 'https://dev.to', '开发者社区', 3),

-- 文档资料
(12, 'GitHub Docs', 'https://docs.github.com', 'GitHub官方文档', 1),
(12, 'Vue.js文档', 'https://vuejs.org', 'Vue.js官方文档', 2),
(12, 'React文档', 'https://reactjs.org', 'React官方文档', 3);
