-- 书签系统核心表（无外键约束版本）
-- 1. 主分类表
CREATE TABLE IF NOT EXISTS categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT,
    category_title VARCHAR(100) NOT NULL,
    category_icon VARCHAR(50) NULL,
    category_color VARCHAR(50) NULL,
    category_sort INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_sort (category_sort)
);

-- 2. 子分类表
CREATE TABLE IF NOT EXISTS subcategories (
    subcategory_id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL COMMENT '关联主分类ID',
    subcategory_title VARCHAR(100) NOT NULL,
    subcategory_icon VARCHAR(50) NULL,
    subcategory_color VARCHAR(50) NULL,
    subcategory_sort INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_sort (category_id, subcategory_sort)
);

-- 3. 书签表
CREATE TABLE IF NOT EXISTS bookmarks (
    bookmark_id INT PRIMARY KEY AUTO_INCREMENT,
    subcategory_id INT NOT NULL COMMENT '关联子分类ID',
    bookmark_title VARCHAR(200) NOT NULL,
    bookmark_url TEXT NOT NULL,
    bookmark_description TEXT NULL,
    bookmark_icon VARCHAR(50) NULL,
    bookmark_sort INT DEFAULT 0,
    click_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by INT NULL COMMENT '创建者用户ID',
    INDEX idx_subcategory_sort (subcategory_id, bookmark_sort),
    INDEX idx_click_count (click_count),
    INDEX idx_created_by (created_by)
);

-- 插入示例数据
INSERT INTO categories (category_title, category_icon, category_sort) VALUES
('常用工具', 'mdi-tools', 1),
('开发资源', 'mdi-code-tags', 2),
('设计素材', 'mdi-palette', 3),
('学习教育', 'mdi-school', 4);

INSERT INTO subcategories (category_id, subcategory_title, subcategory_icon, subcategory_sort) VALUES
(1, '搜索引擎', 'mdi-magnify', 1),
(1, '在线工具', 'mdi-wrench', 2),
(1, '云存储', 'mdi-cloud', 3),
(2, '代码托管', 'mdi-git', 1),
(2, '开发文档', 'mdi-book-open', 2),
(2, '在线编辑器', 'mdi-code-braces', 3),
(3, '图标素材', 'mdi-image', 1),
(3, '字体资源', 'mdi-format-font', 2),
(3, '配色工具', 'mdi-palette-swatch', 3),
(4, '在线课程', 'mdi-play-circle', 1),
(4, '技术博客', 'mdi-post', 2),
(4, '文档资料', 'mdi-file-document', 3);

INSERT INTO bookmarks (subcategory_id, bookmark_title, bookmark_url, bookmark_description, bookmark_sort) VALUES
-- 搜索引擎
(1, 'Google', 'https://www.google.com', '全球最大的搜索引擎', 1),
(1, '百度', 'https://www.baidu.com', '中国最大的搜索引擎', 2),
(1, 'Bing', 'https://www.bing.com', '微软搜索引擎', 3),

-- 在线工具
(2, 'JSON格式化', 'https://jsonformatter.org', '在线JSON格式化和验证工具', 1),
(2, '正则表达式测试', 'https://regex101.com', '在线正则表达式测试工具', 2),
(2, 'Base64编解码', 'https://base64decode.org', '在线Base64编解码工具', 3),

-- 云存储
(3, 'Google Drive', 'https://drive.google.com', '谷歌云存储服务', 1),
(3, '百度网盘', 'https://pan.baidu.com', '百度云存储服务', 2),
(3, 'OneDrive', 'https://onedrive.live.com', '微软云存储服务', 3),

-- 代码托管
(4, 'GitHub', 'https://github.com', '全球最大的代码托管平台', 1),
(4, 'GitLab', 'https://gitlab.com', '开源的代码托管平台', 2),
(4, 'Gitee', 'https://gitee.com', '中国的代码托管平台', 3),

-- 开发文档
(5, 'MDN Web Docs', 'https://developer.mozilla.org', 'Web开发权威文档', 1),
(5, 'Stack Overflow', 'https://stackoverflow.com', '程序员问答社区', 2),
(5, 'W3Schools', 'https://www.w3schools.com', 'Web技术教程网站', 3),

-- 在线编辑器
(6, 'CodePen', 'https://codepen.io', '前端代码在线编辑器', 1),
(6, 'JSFiddle', 'https://jsfiddle.net', 'JavaScript在线编辑器', 2),
(6, 'Repl.it', 'https://replit.com', '多语言在线编程环境', 3),

-- 图标素材
(7, 'Iconfont', 'https://www.iconfont.cn', '阿里巴巴图标库', 1),
(7, 'Font Awesome', 'https://fontawesome.com', '流行的图标字体库', 2),
(7, 'Feather Icons', 'https://feathericons.com', '简洁的开源图标集', 3),

-- 字体资源
(8, 'Google Fonts', 'https://fonts.google.com', '谷歌免费字体库', 1),
(8, '字体天下', 'https://www.fonts.net.cn', '中文字体资源网站', 2),
(8, 'DaFont', 'https://www.dafont.com', '免费字体下载网站', 3),

-- 配色工具
(9, 'Coolors', 'https://coolors.co', '在线配色方案生成器', 1),
(9, 'Adobe Color', 'https://color.adobe.com', 'Adobe配色工具', 2),
(9, 'Paletton', 'https://paletton.com', '专业配色工具', 3),

-- 在线课程
(10, 'Coursera', 'https://www.coursera.org', '国际知名在线课程平台', 1),
(10, '慕课网', 'https://www.imooc.com', '程序员在线学习平台', 2),
(10, 'edX', 'https://www.edx.org', '哈佛MIT等名校课程平台', 3),

-- 技术博客
(11, '掘金', 'https://juejin.cn', '中国技术社区', 1),
(11, 'Medium', 'https://medium.com', '国际技术博客平台', 2),
(11, 'Dev.to', 'https://dev.to', '开发者社区', 3),

-- 文档资料
(12, 'GitHub Docs', 'https://docs.github.com', 'GitHub官方文档', 1),
(12, 'Vue.js文档', 'https://vuejs.org', 'Vue.js官方文档', 2),
(12, 'React文档', 'https://reactjs.org', 'React官方文档', 3);
