@echo off
setlocal enabledelayedexpansion

title Start Project

cls
echo.
echo ========================================
echo    Bookmarks Navigator - Start
echo ========================================
echo.

echo [1/4] Checking Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js and run install.bat
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo OK: Node.js !NODE_VERSION!
)

echo.
echo [2/4] Checking dependencies...
if not exist "node_modules" (
    echo ERROR: node_modules not found
    echo Please run install.bat first
    pause
    exit /b 1
) else (
    echo OK: Dependencies installed
)

echo.
echo [3/4] Checking project files...
if not exist "server.js" (
    echo ERROR: server.js not found
    echo Please run this script in project root directory
    pause
    exit /b 1
) else (
    echo OK: Project files found
)

echo.
echo [4/4] Getting configuration...
set PORT=3006
if exist ".env" (
    for /f "usebackq tokens=1,2 delims==" %%a in (".env") do (
        if /i "%%a"=="PORT" set PORT=%%b
    )
)

set PORT=!PORT: =!
if "!PORT!"=="" set PORT=3006

echo OK: Configuration ready (Port: !PORT!)

echo.
echo ========================================
echo Starting Bookmarks Navigator...
echo ========================================
echo.
echo Service Info:
echo   - Node.js: !NODE_VERSION!
echo   - Port: !PORT!
echo   - Mode: Development
echo.
echo Access URLs:
echo   - Home: http://127.0.0.1:!PORT!
echo   - Admin: http://127.0.0.1:!PORT!/admin
echo   - Login: http://127.0.0.1:!PORT!/login
echo.
echo Admin Login:
echo   - Password: admin123456
echo.
echo ========================================
echo Press Ctrl+C to stop the server
echo ========================================
echo.

call npm run dev

if errorlevel 1 (
    echo.
    echo ERROR: Server failed to start
    echo Check port !PORT! availability and database connection
    echo.
) else (
    echo.
    echo Server stopped normally
)

echo.
pause
exit /b
