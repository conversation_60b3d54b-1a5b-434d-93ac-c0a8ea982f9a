const db = require('../config/database');

const mysqlDb = {
    // 获取所有主分类及其子分类和书签
    async getCategories() {
        try {
            // 获取所有主分类
            const [categories] = await db.execute(`
                SELECT * FROM categories
                ORDER BY category_sort, category_id
            `);

            // 为每个主分类获取子分类和书签
            for (const category of categories) {
                const [subcategories] = await db.execute(`
                    SELECT * FROM subcategories
                    WHERE category_id = ?
                    ORDER BY subcategory_sort, subcategory_id
                `, [category.category_id]);

                // 为每个子分类获取书签
                for (const subcat of subcategories) {
                    const [bookmarks] = await db.execute(`
                        SELECT * FROM bookmarks
                        WHERE subcategory_id = ?
                        ORDER BY bookmark_sort, bookmark_id
                    `, [subcat.subcategory_id]);
                    subcat.links = bookmarks;
                }

                category.subcategories = subcategories;
            }

            return categories;
        } catch (error) {
            console.error('获取分类错误:', error);
            throw error;
        }
    },

    // 获取特定分类的子分类及其书签
    async getCategoryWithSubcategories(categoryId) {
        try {
            // 获取子分类
            const [subcategories] = await db.execute(`
                SELECT * FROM subcategories
                WHERE category_id = ?
                ORDER BY subcategory_sort, subcategory_id
            `, [categoryId]);

            // 获取每个子分类的书签
            for (const subcat of subcategories) {
                const [bookmarks] = await db.execute(`
                    SELECT * FROM bookmarks
                    WHERE subcategory_id = ?
                    ORDER BY bookmark_sort, bookmark_id
                `, [subcat.subcategory_id]);
                subcat.links = bookmarks;
            }

            return subcategories;
        } catch (error) {
            console.error('获取分类详情错误:', error);
            throw error;
        }
    },

    // 更新书签点击次数
    async incrementBookmarkClicks(bookmarkId) {
        try {
            await db.execute(`
                UPDATE bookmarks
                SET click_count = click_count + 1
                WHERE bookmark_id = ?
            `, [bookmarkId]);

            const [rows] = await db.execute(`
                SELECT * FROM bookmarks
                WHERE bookmark_id = ?
            `, [bookmarkId]);

            return rows[0];
        } catch (error) {
            console.error('更新点击次数错误:', error);
            throw error;
        }
    },

    // 直接执行SQL查询（用于认证等功能）
    async execute(sql, params = []) {
        try {
            return await db.execute(sql, params);
        } catch (error) {
            console.error('SQL执行错误:', error);
            throw error;
        }
    },

    // 直接查询（用于简单查询）
    async query(sql, params = []) {
        try {
            return await db.query(sql, params);
        } catch (error) {
            console.error('SQL查询错误:', error);
            throw error;
        }
    }
};

module.exports = mysqlDb; 